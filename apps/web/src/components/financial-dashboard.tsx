import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
	DollarSign, 
	TrendingUp, 
	TrendingDown, 
	CreditCard, 
	Wallet,
	Building2,
	ArrowUpRight,
	ArrowDownRight,
	RefreshCw,
	Plus,
	Filter,
	Calendar,
	PieChart,
	BarChart3
} from "lucide-react";
import { toast } from "sonner";
import { orpc } from "@/utils/orpc";
import { BankConnection } from "./bank-connection";
import { AIInsights } from "./ai-insights";

interface BankAccount {
	id: string;
	accountName: string;
	accountType: string;
	balance: string;
	availableBalance: string;
	currency: string;
	lastSyncAt: string;
	isActive: boolean;
}

interface Transaction {
	id: string;
	amount: string;
	currency: string;
	description: string;
	merchantName?: string;
	date: string;
	type: "income" | "expense" | "transfer";
	categoryId?: string;
	accountId: string;
}

interface BalanceSummary {
	totalBalance: number;
	totalAvailable: number;
	accountsByType: Record<string, { count: number; balance: number }>;
	lastSyncAt: Date | null;
}

interface SpendingInsights {
	totalSpending: number;
	totalIncome: number;
	netIncome: number;
	transactionCount: number;
	categorySpending: Record<string, number>;
	averageTransactionAmount: number;
}

export function FinancialDashboard() {
	const [isLoading, setIsLoading] = useState(false);
	const [accounts, setAccounts] = useState<BankAccount[]>([]);
	const [transactions, setTransactions] = useState<Transaction[]>([]);
	const [balanceSummary, setBalanceSummary] = useState<BalanceSummary | null>(null);
	const [spendingInsights, setSpendingInsights] = useState<SpendingInsights | null>(null);
	const [activeTab, setActiveTab] = useState("overview");

	useEffect(() => {
		fetchDashboardData();
	}, []);

	const fetchDashboardData = async () => {
		setIsLoading(true);
		try {
			const [accountsResponse, balanceResponse, transactionsResponse, insightsResponse] = 
				await Promise.all([
					orpc.banking.getUserBankAccounts.query(),
					orpc.banking.getUserBalanceSummary.query(),
					orpc.banking.getUserTransactions.query({ limit: 50 }),
					orpc.banking.getSpendingInsights.query({ period: "month" }),
				]);

			if (accountsResponse.success) {
				setAccounts(accountsResponse.accounts);
			}

			if (balanceResponse.success) {
				setBalanceSummary(balanceResponse.summary);
			}

			if (transactionsResponse.success) {
				setTransactions(transactionsResponse.transactions);
			}

			if (insightsResponse.success) {
				setSpendingInsights(insightsResponse.insights);
			}
		} catch (error) {
			console.error("Error fetching dashboard data:", error);
			toast.error("Failed to load dashboard data");
		} finally {
			setIsLoading(false);
		}
	};

	const syncAllAccounts = async () => {
		setIsLoading(true);
		try {
			// This would trigger sync for all connected accounts
			await fetchDashboardData();
			toast.success("All accounts synced successfully");
		} catch (error) {
			console.error("Error syncing accounts:", error);
			toast.error("Failed to sync accounts");
		} finally {
			setIsLoading(false);
		}
	};

	const formatCurrency = (amount: number | string, currency = "USD") => {
		const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: currency,
		}).format(numAmount);
	};

	const formatAccountType = (type: string) => {
		return type.replace("_", " ").replace(/\b\w/g, (l) => l.toUpperCase());
	};

	const getAccountIcon = (accountType: string) => {
		switch (accountType) {
			case "checking":
			case "savings":
				return <Wallet className="h-5 w-5" />;
			case "credit_card":
				return <CreditCard className="h-5 w-5" />;
			default:
				return <Building2 className="h-5 w-5" />;
		}
	};

	const getTransactionIcon = (type: string) => {
		switch (type) {
			case "income":
				return <ArrowUpRight className="h-4 w-4 text-green-500" />;
			case "expense":
				return <ArrowDownRight className="h-4 w-4 text-red-500" />;
			default:
				return <RefreshCw className="h-4 w-4 text-blue-500" />;
		}
	};

	const getNetIncomeColor = (netIncome: number) => {
		if (netIncome > 0) return "text-green-600";
		if (netIncome < 0) return "text-red-600";
		return "text-gray-600";
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Financial Dashboard</h1>
					<p className="text-muted-foreground">
						Your complete financial overview
					</p>
				</div>
				<div className="flex gap-2">
					<Button variant="outline" onClick={syncAllAccounts} disabled={isLoading}>
						<RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
						Sync All
					</Button>
				</div>
			</div>

			{/* Balance Summary Cards */}
			{balanceSummary && (
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Total Balance</CardTitle>
							<DollarSign className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{formatCurrency(balanceSummary.totalBalance)}
							</div>
							<p className="text-xs text-muted-foreground">
								Across {accounts.length} accounts
							</p>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Available Balance</CardTitle>
							<Wallet className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{formatCurrency(balanceSummary.totalAvailable)}
							</div>
							<p className="text-xs text-muted-foreground">
								Available to spend
							</p>
						</CardContent>
					</Card>

					{spendingInsights && (
						<>
							<Card>
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<CardTitle className="text-sm font-medium">This Month</CardTitle>
									<TrendingUp className="h-4 w-4 text-muted-foreground" />
								</CardHeader>
								<CardContent>
									<div className="text-2xl font-bold">
										{formatCurrency(spendingInsights.totalSpending)}
									</div>
									<p className="text-xs text-muted-foreground">
										Total spending
									</p>
								</CardContent>
							</Card>

							<Card>
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<CardTitle className="text-sm font-medium">Net Income</CardTitle>
									<BarChart3 className="h-4 w-4 text-muted-foreground" />
								</CardHeader>
								<CardContent>
									<div className={`text-2xl font-bold ${getNetIncomeColor(spendingInsights.netIncome)}`}>
										{formatCurrency(spendingInsights.netIncome)}
									</div>
									<p className="text-xs text-muted-foreground">
										Income - Expenses
									</p>
								</CardContent>
							</Card>
						</>
					)}
				</div>
			)}

			<Tabs value={activeTab} onValueChange={setActiveTab}>
				<TabsList className="grid w-full grid-cols-5">
					<TabsTrigger value="overview">Overview</TabsTrigger>
					<TabsTrigger value="accounts">Accounts</TabsTrigger>
					<TabsTrigger value="transactions">Transactions</TabsTrigger>
					<TabsTrigger value="insights">AI Insights</TabsTrigger>
					<TabsTrigger value="connect">Connect Banks</TabsTrigger>
				</TabsList>

				<TabsContent value="overview" className="space-y-6">
					{/* Account Overview */}
					<Card>
						<CardHeader>
							<CardTitle>Account Overview</CardTitle>
							<CardDescription>Your connected bank accounts</CardDescription>
						</CardHeader>
						<CardContent>
							{accounts.length > 0 ? (
								<div className="space-y-4">
									{accounts.slice(0, 5).map((account) => (
										<div key={account.id} className="flex items-center justify-between p-3 border rounded-lg">
											<div className="flex items-center gap-3">
												{getAccountIcon(account.accountType)}
												<div>
													<h3 className="font-medium">{account.accountName}</h3>
													<p className="text-sm text-muted-foreground">
														{formatAccountType(account.accountType)}
													</p>
												</div>
											</div>
											<div className="text-right">
												<div className="font-semibold">
													{formatCurrency(account.balance, account.currency)}
												</div>
												<p className="text-sm text-muted-foreground">
													Available: {formatCurrency(account.availableBalance, account.currency)}
												</p>
											</div>
										</div>
									))}
									{accounts.length > 5 && (
										<Button variant="outline" onClick={() => setActiveTab("accounts")}>
											View All {accounts.length} Accounts
										</Button>
									)}
								</div>
							) : (
								<div className="text-center py-8">
									<Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
									<h3 className="text-lg font-semibold mb-2">No accounts connected</h3>
									<p className="text-muted-foreground mb-4">
										Connect your bank accounts to get started
									</p>
									<Button onClick={() => setActiveTab("connect")}>
										<Plus className="h-4 w-4 mr-2" />
										Connect Bank Account
									</Button>
								</div>
							)}
						</CardContent>
					</Card>

					{/* Recent Transactions */}
					<Card>
						<CardHeader>
							<CardTitle>Recent Transactions</CardTitle>
							<CardDescription>Your latest financial activity</CardDescription>
						</CardHeader>
						<CardContent>
							{transactions.length > 0 ? (
								<div className="space-y-3">
									{transactions.slice(0, 10).map((transaction) => (
										<div key={transaction.id} className="flex items-center justify-between p-3 border rounded-lg">
											<div className="flex items-center gap-3">
												{getTransactionIcon(transaction.type)}
												<div>
													<h3 className="font-medium">
														{transaction.merchantName || transaction.description}
													</h3>
													<p className="text-sm text-muted-foreground">
														{new Date(transaction.date).toLocaleDateString()}
													</p>
												</div>
											</div>
											<div className={`font-semibold ${
												transaction.type === "income" ? "text-green-600" : "text-red-600"
											}`}>
												{transaction.type === "income" ? "+" : "-"}
												{formatCurrency(transaction.amount, transaction.currency)}
											</div>
										</div>
									))}
									<Button variant="outline" onClick={() => setActiveTab("transactions")}>
										View All Transactions
									</Button>
								</div>
							) : (
								<div className="text-center py-8">
									<BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
									<h3 className="text-lg font-semibold mb-2">No transactions yet</h3>
									<p className="text-muted-foreground">
										Transactions will appear here once you connect your accounts
									</p>
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="accounts" className="space-y-4">
					{accounts.length > 0 ? (
						<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
							{accounts.map((account) => (
								<Card key={account.id}>
									<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
										<CardTitle className="text-sm font-medium flex items-center gap-2">
											{getAccountIcon(account.accountType)}
											{account.accountName}
										</CardTitle>
										<Badge variant={account.isActive ? "default" : "secondary"}>
											{account.isActive ? "Active" : "Inactive"}
										</Badge>
									</CardHeader>
									<CardContent>
										<div className="text-2xl font-bold">
											{formatCurrency(account.balance, account.currency)}
										</div>
										<p className="text-xs text-muted-foreground">
											Available: {formatCurrency(account.availableBalance, account.currency)}
										</p>
										<p className="text-xs text-muted-foreground mt-2">
											{formatAccountType(account.accountType)}
										</p>
										{account.lastSyncAt && (
											<p className="text-xs text-muted-foreground mt-1">
												Last synced: {new Date(account.lastSyncAt).toLocaleDateString()}
											</p>
										)}
									</CardContent>
								</Card>
							))}
						</div>
					) : (
						<Card>
							<CardContent className="flex flex-col items-center justify-center py-12">
								<Building2 className="h-12 w-12 text-muted-foreground mb-4" />
								<h3 className="text-lg font-semibold mb-2">No bank accounts connected</h3>
								<p className="text-muted-foreground text-center mb-4">
									Connect your bank accounts to start tracking your finances
								</p>
								<Button onClick={() => setActiveTab("connect")}>
									<Plus className="h-4 w-4 mr-2" />
									Connect Your First Bank
								</Button>
							</CardContent>
						</Card>
					)}
				</TabsContent>

				<TabsContent value="transactions" className="space-y-4">
					<div className="flex items-center justify-between">
						<h3 className="text-lg font-semibold">All Transactions</h3>
						<div className="flex gap-2">
							<Button variant="outline" size="sm">
								<Filter className="h-4 w-4 mr-2" />
								Filter
							</Button>
							<Button variant="outline" size="sm">
								<Calendar className="h-4 w-4 mr-2" />
								Date Range
							</Button>
						</div>
					</div>

					{transactions.length > 0 ? (
						<div className="space-y-2">
							{transactions.map((transaction) => (
								<Card key={transaction.id}>
									<CardContent className="pt-4">
										<div className="flex items-center justify-between">
											<div className="flex items-center gap-3">
												{getTransactionIcon(transaction.type)}
												<div>
													<h3 className="font-medium">
														{transaction.merchantName || transaction.description}
													</h3>
													<p className="text-sm text-muted-foreground">
														{new Date(transaction.date).toLocaleDateString()} • 
														{formatAccountType(transaction.type)}
													</p>
												</div>
											</div>
											<div className={`font-semibold ${
												transaction.type === "income" ? "text-green-600" : "text-red-600"
											}`}>
												{transaction.type === "income" ? "+" : "-"}
												{formatCurrency(transaction.amount, transaction.currency)}
											</div>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					) : (
						<Card>
							<CardContent className="flex flex-col items-center justify-center py-12">
								<BarChart3 className="h-12 w-12 text-muted-foreground mb-4" />
								<h3 className="text-lg font-semibold mb-2">No transactions found</h3>
								<p className="text-muted-foreground text-center">
									Transactions will appear here once you connect your bank accounts
								</p>
							</CardContent>
						</Card>
					)}
				</TabsContent>

				<TabsContent value="insights">
					<AIInsights />
				</TabsContent>

				<TabsContent value="connect">
					<BankConnection />
				</TabsContent>
			</Tabs>
		</div>
	);
}
