import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
	Search, 
	Filter, 
	Tag, 
	ArrowUpRight, 
	ArrowDownRight, 
	RefreshCw,
	Check,
	X,
	Brain,
	Loader2
} from "lucide-react";
import { toast } from "sonner";
import { orpc } from "@/utils/orpc";

interface Transaction {
	id: string;
	amount: string;
	currency: string;
	description: string;
	merchantName?: string;
	date: string;
	type: "income" | "expense" | "transfer";
	categoryId?: string;
	accountId: string;
}

interface Category {
	id: string;
	name: string;
	icon: string;
	color: string;
}

export function TransactionCategorization() {
	const [isLoading, setIsLoading] = useState(false);
	const [transactions, setTransactions] = useState<Transaction[]>([]);
	const [categories, setCategories] = useState<Category[]>([]);
	const [uncategorizedTransactions, setUncategorizedTransactions] = useState<Transaction[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedCategory, setSelectedCategory] = useState<string>("");
	const [filterType, setFilterType] = useState<"all" | "income" | "expense" | "transfer">("all");

	useEffect(() => {
		fetchData();
	}, []);

	const fetchData = async () => {
		setIsLoading(true);
		try {
			const [transactionsResponse, categoriesResponse] = await Promise.all([
				orpc.banking.getUserTransactions.query({ limit: 200 }),
				orpc.banking.getTransactionCategories.query(),
			]);

			if (transactionsResponse.success) {
				setTransactions(transactionsResponse.transactions);
				// Filter uncategorized transactions
				const uncategorized = transactionsResponse.transactions.filter(
					(t) => !t.categoryId || t.categoryId === ""
				);
				setUncategorizedTransactions(uncategorized);
			}

			if (categoriesResponse.success) {
				setCategories(categoriesResponse.categories);
			}
		} catch (error) {
			console.error("Error fetching data:", error);
			toast.error("Failed to load transactions and categories");
		} finally {
			setIsLoading(false);
		}
	};

	const categorizeTransaction = async (transactionId: string, categoryId: string) => {
		try {
			const response = await orpc.banking.updateTransactionCategory.mutate({
				transactionId,
				categoryId,
			});

			if (response.success) {
				// Update local state
				setTransactions(transactions.map(t => 
					t.id === transactionId ? { ...t, categoryId } : t
				));
				setUncategorizedTransactions(uncategorizedTransactions.filter(t => t.id !== transactionId));
				toast.success("Transaction categorized successfully");
			}
		} catch (error) {
			console.error("Error categorizing transaction:", error);
			toast.error("Failed to categorize transaction");
		}
	};

	const autoCategorizeBatch = async (transactionIds: string[]) => {
		setIsLoading(true);
		try {
			for (const transactionId of transactionIds) {
				const transaction = transactions.find(t => t.id === transactionId);
				if (!transaction) continue;

				// Use AI to suggest category
				const response = await orpc.ai.categorizeTransaction.query({
					description: transaction.description,
					amount: parseFloat(transaction.amount),
					merchantName: transaction.merchantName,
				});

				if (response.success) {
					// Find category by name
					const category = categories.find(c => c.name === response.category);
					if (category) {
						await categorizeTransaction(transactionId, category.id);
					}
				}
			}
			toast.success("Batch categorization completed");
		} catch (error) {
			console.error("Error in batch categorization:", error);
			toast.error("Failed to auto-categorize transactions");
		} finally {
			setIsLoading(false);
		}
	};

	const filteredTransactions = transactions.filter((transaction) => {
		const matchesSearch = 
			transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
			(transaction.merchantName?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false);
		
		const matchesType = filterType === "all" || transaction.type === filterType;
		
		const matchesCategory = selectedCategory === "" || 
			(selectedCategory === "uncategorized" && !transaction.categoryId) ||
			transaction.categoryId === selectedCategory;

		return matchesSearch && matchesType && matchesCategory;
	});

	const formatCurrency = (amount: string, currency = "USD") => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: currency,
		}).format(parseFloat(amount));
	};

	const getTransactionIcon = (type: string) => {
		switch (type) {
			case "income":
				return <ArrowUpRight className="h-4 w-4 text-green-500" />;
			case "expense":
				return <ArrowDownRight className="h-4 w-4 text-red-500" />;
			default:
				return <RefreshCw className="h-4 w-4 text-blue-500" />;
		}
	};

	const getCategoryBadge = (categoryId?: string) => {
		if (!categoryId) {
			return <Badge variant="outline">Uncategorized</Badge>;
		}
		
		const category = categories.find(c => c.id === categoryId);
		if (!category) {
			return <Badge variant="outline">Unknown</Badge>;
		}

		return (
			<Badge variant="secondary" className="flex items-center gap-1">
				<span>{category.icon}</span>
				{category.name}
			</Badge>
		);
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h2 className="text-2xl font-bold">Transaction Categorization</h2>
					<p className="text-muted-foreground">
						Organize your transactions with categories
					</p>
				</div>
				<div className="flex gap-2">
					{uncategorizedTransactions.length > 0 && (
						<Button 
							variant="outline" 
							onClick={() => autoCategorizeBatch(uncategorizedTransactions.slice(0, 10).map(t => t.id))}
							disabled={isLoading}
						>
							<Brain className="h-4 w-4 mr-2" />
							Auto-Categorize
						</Button>
					)}
					<Button variant="outline" onClick={fetchData} disabled={isLoading}>
						<RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
						Refresh
					</Button>
				</div>
			</div>

			{/* Summary Cards */}
			<div className="grid gap-4 md:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
						<Tag className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{transactions.length}</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Uncategorized</CardTitle>
						<X className="h-4 w-4 text-red-500" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-red-600">
							{uncategorizedTransactions.length}
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Categorized</CardTitle>
						<Check className="h-4 w-4 text-green-500" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-green-600">
							{transactions.length - uncategorizedTransactions.length}
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Categories</CardTitle>
						<Tag className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{categories.length}</div>
					</CardContent>
				</Card>
			</div>

			{/* Filters */}
			<Card>
				<CardHeader>
					<CardTitle>Filters</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid gap-4 md:grid-cols-4">
						<div className="space-y-2">
							<Label htmlFor="search">Search</Label>
							<div className="relative">
								<Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
								<Input
									id="search"
									placeholder="Search transactions..."
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className="pl-8"
								/>
							</div>
						</div>

						<div className="space-y-2">
							<Label htmlFor="type">Transaction Type</Label>
							<Select value={filterType} onValueChange={(value: any) => setFilterType(value)}>
								<SelectTrigger>
									<SelectValue placeholder="All types" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Types</SelectItem>
									<SelectItem value="income">Income</SelectItem>
									<SelectItem value="expense">Expense</SelectItem>
									<SelectItem value="transfer">Transfer</SelectItem>
								</SelectContent>
							</Select>
						</div>

						<div className="space-y-2">
							<Label htmlFor="category">Category</Label>
							<Select value={selectedCategory} onValueChange={setSelectedCategory}>
								<SelectTrigger>
									<SelectValue placeholder="All categories" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="">All Categories</SelectItem>
									<SelectItem value="uncategorized">Uncategorized</SelectItem>
									{categories.map((category) => (
										<SelectItem key={category.id} value={category.id}>
											{category.icon} {category.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className="space-y-2">
							<Label>&nbsp;</Label>
							<Button 
								variant="outline" 
								onClick={() => {
									setSearchTerm("");
									setFilterType("all");
									setSelectedCategory("");
								}}
								className="w-full"
							>
								<Filter className="h-4 w-4 mr-2" />
								Clear Filters
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Transactions List */}
			<Card>
				<CardHeader>
					<CardTitle>Transactions ({filteredTransactions.length})</CardTitle>
					<CardDescription>
						Click on a transaction to categorize it
					</CardDescription>
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="flex items-center justify-center py-8">
							<Loader2 className="h-8 w-8 animate-spin" />
							<span className="ml-2">Loading transactions...</span>
						</div>
					) : filteredTransactions.length > 0 ? (
						<div className="space-y-3">
							{filteredTransactions.map((transaction) => (
								<TransactionRow
									key={transaction.id}
									transaction={transaction}
									categories={categories}
									onCategorize={categorizeTransaction}
									formatCurrency={formatCurrency}
									getTransactionIcon={getTransactionIcon}
									getCategoryBadge={getCategoryBadge}
								/>
							))}
						</div>
					) : (
						<div className="text-center py-8">
							<Tag className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
							<h3 className="text-lg font-semibold mb-2">No transactions found</h3>
							<p className="text-muted-foreground">
								Try adjusting your filters or search terms
							</p>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}

interface TransactionRowProps {
	transaction: Transaction;
	categories: Category[];
	onCategorize: (transactionId: string, categoryId: string) => void;
	formatCurrency: (amount: string, currency?: string) => string;
	getTransactionIcon: (type: string) => JSX.Element;
	getCategoryBadge: (categoryId?: string) => JSX.Element;
}

function TransactionRow({ 
	transaction, 
	categories, 
	onCategorize, 
	formatCurrency, 
	getTransactionIcon, 
	getCategoryBadge 
}: TransactionRowProps) {
	const [isEditing, setIsEditing] = useState(false);
	const [selectedCategoryId, setSelectedCategoryId] = useState(transaction.categoryId || "");

	const handleSave = () => {
		if (selectedCategoryId && selectedCategoryId !== transaction.categoryId) {
			onCategorize(transaction.id, selectedCategoryId);
		}
		setIsEditing(false);
	};

	const handleCancel = () => {
		setSelectedCategoryId(transaction.categoryId || "");
		setIsEditing(false);
	};

	return (
		<div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50">
			<div className="flex items-center gap-3 flex-1">
				{getTransactionIcon(transaction.type)}
				<div className="flex-1">
					<h3 className="font-medium">
						{transaction.merchantName || transaction.description}
					</h3>
					<p className="text-sm text-muted-foreground">
						{new Date(transaction.date).toLocaleDateString()}
					</p>
				</div>
			</div>

			<div className="flex items-center gap-4">
				<div className={`font-semibold ${
					transaction.type === "income" ? "text-green-600" : "text-red-600"
				}`}>
					{transaction.type === "income" ? "+" : "-"}
					{formatCurrency(transaction.amount, transaction.currency)}
				</div>

				{isEditing ? (
					<div className="flex items-center gap-2">
						<Select value={selectedCategoryId} onValueChange={setSelectedCategoryId}>
							<SelectTrigger className="w-40">
								<SelectValue placeholder="Select category" />
							</SelectTrigger>
							<SelectContent>
								{categories.map((category) => (
									<SelectItem key={category.id} value={category.id}>
										{category.icon} {category.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
						<Button size="sm" onClick={handleSave}>
							<Check className="h-4 w-4" />
						</Button>
						<Button size="sm" variant="outline" onClick={handleCancel}>
							<X className="h-4 w-4" />
						</Button>
					</div>
				) : (
					<div className="flex items-center gap-2">
						{getCategoryBadge(transaction.categoryId)}
						<Button 
							size="sm" 
							variant="ghost" 
							onClick={() => setIsEditing(true)}
						>
							<Tag className="h-4 w-4" />
						</Button>
					</div>
				)}
			</div>
		</div>
	);
}
