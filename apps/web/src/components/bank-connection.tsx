import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Loader2, Plus, Building2, CreditCard, Wallet, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { orpc } from "@/utils/orpc";

interface BankAccount {
	id: string;
	accountName: string;
	accountType: string;
	balance: string;
	currency: string;
	lastSyncAt: string;
	isActive: boolean;
}

interface Institution {
	id: string;
	name: string;
	logo?: string;
	country_code: string;
}

export function BankConnection() {
	const [isLoading, setIsLoading] = useState(false);
	const [accounts, setAccounts] = useState<BankAccount[]>([]);
	const [showConnectionForm, setShowConnectionForm] = useState(false);
	const [provider, setProvider] = useState<"plaid" | "belvo" | null>(null);
	const [institutions, setInstitutions] = useState<Institution[]>([]);
	const [selectedInstitution, setSelectedInstitution] = useState<string>("");
	const [credentials, setCredentials] = useState({
		username: "",
		password: "",
		username2: "",
		username3: "",
	});

	// Fetch user's bank accounts on component mount
	useEffect(() => {
		fetchBankAccounts();
	}, []);

	const fetchBankAccounts = async () => {
		try {
			const response = await orpc.banking.getUserBankAccounts.query();
			if (response.success) {
				setAccounts(response.accounts);
			}
		} catch (error) {
			console.error("Error fetching bank accounts:", error);
			toast.error("Failed to fetch bank accounts");
		}
	};

	const getRecommendedProvider = async () => {
		try {
			// Get user's country code (you might want to get this from user profile)
			const countryCode = "US"; // Default to US, should be dynamic
			const response = await orpc.banking.getRecommendedProvider.query({ countryCode });
			setProvider(response.provider);
			return response.provider;
		} catch (error) {
			console.error("Error getting recommended provider:", error);
			setProvider("plaid"); // Default fallback
			return "plaid";
		}
	};

	const initiateBankConnection = async () => {
		setIsLoading(true);
		try {
			const recommendedProvider = await getRecommendedProvider();
			
			const response = await orpc.banking.initiateBankConnection.query({
				provider: recommendedProvider,
				countryCode: "US",
			});

			if (response.success) {
				if (recommendedProvider === "plaid") {
					// Handle Plaid Link
					handlePlaidLink(response.data.link_token);
				} else {
					// Handle Belvo institutions
					setInstitutions(response.data);
					setShowConnectionForm(true);
				}
			}
		} catch (error) {
			console.error("Error initiating bank connection:", error);
			toast.error("Failed to initiate bank connection");
		} finally {
			setIsLoading(false);
		}
	};

	const handlePlaidLink = (linkToken: string) => {
		// In a real implementation, you would use Plaid Link SDK here
		// For now, we'll simulate the process
		console.log("Opening Plaid Link with token:", linkToken);
		
		// Simulate successful connection
		setTimeout(() => {
			const mockPublicToken = "public-sandbox-" + Math.random().toString(36).substr(2, 9);
			completePlaidConnection(mockPublicToken);
		}, 2000);
	};

	const completePlaidConnection = async (publicToken: string) => {
		try {
			const response = await orpc.banking.completeBankConnection.mutate({
				provider: "plaid",
				connectionData: { publicToken },
			});

			if (response.success) {
				toast.success("Bank connected successfully!");
				setShowConnectionForm(false);
				fetchBankAccounts();
			}
		} catch (error) {
			console.error("Error completing Plaid connection:", error);
			toast.error("Failed to complete bank connection");
		}
	};

	const completeBelvoConnection = async () => {
		if (!selectedInstitution || !credentials.username || !credentials.password) {
			toast.error("Please fill in all required fields");
			return;
		}

		setIsLoading(true);
		try {
			const response = await orpc.banking.completeBankConnection.mutate({
				provider: "belvo",
				connectionData: {
					institution: selectedInstitution,
					username: credentials.username,
					password: credentials.password,
					username2: credentials.username2 || undefined,
					username3: credentials.username3 || undefined,
				},
			});

			if (response.success) {
				toast.success("Bank connected successfully!");
				setShowConnectionForm(false);
				setCredentials({ username: "", password: "", username2: "", username3: "" });
				setSelectedInstitution("");
				fetchBankAccounts();
			}
		} catch (error) {
			console.error("Error completing Belvo connection:", error);
			toast.error("Failed to complete bank connection");
		} finally {
			setIsLoading(false);
		}
	};

	const syncAccounts = async () => {
		setIsLoading(true);
		try {
			// This would sync all user accounts
			// For now, just refresh the accounts list
			await fetchBankAccounts();
			toast.success("Accounts synced successfully");
		} catch (error) {
			console.error("Error syncing accounts:", error);
			toast.error("Failed to sync accounts");
		} finally {
			setIsLoading(false);
		}
	};

	const getAccountIcon = (accountType: string) => {
		switch (accountType) {
			case "checking":
			case "savings":
				return <Wallet className="h-5 w-5" />;
			case "credit_card":
				return <CreditCard className="h-5 w-5" />;
			default:
				return <Building2 className="h-5 w-5" />;
		}
	};

	const formatBalance = (balance: string, currency: string) => {
		const amount = parseFloat(balance);
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: currency,
		}).format(amount);
	};

	const formatAccountType = (type: string) => {
		return type.replace("_", " ").replace(/\b\w/g, (l) => l.toUpperCase());
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h2 className="text-2xl font-bold">Bank Accounts</h2>
					<p className="text-muted-foreground">
						Connect your bank accounts to track your finances
					</p>
				</div>
				<div className="flex gap-2">
					<Button variant="outline" onClick={syncAccounts} disabled={isLoading}>
						{isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : "Sync"}
					</Button>
					<Button onClick={initiateBankConnection} disabled={isLoading}>
						<Plus className="h-4 w-4 mr-2" />
						Connect Bank
					</Button>
				</div>
			</div>

			{/* Connected Accounts */}
			{accounts.length > 0 ? (
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
					{accounts.map((account) => (
						<Card key={account.id}>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-sm font-medium flex items-center gap-2">
									{getAccountIcon(account.accountType)}
									{account.accountName}
								</CardTitle>
								<Badge variant={account.isActive ? "default" : "secondary"}>
									{account.isActive ? "Active" : "Inactive"}
								</Badge>
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold">
									{formatBalance(account.balance, account.currency)}
								</div>
								<p className="text-xs text-muted-foreground">
									{formatAccountType(account.accountType)}
								</p>
								{account.lastSyncAt && (
									<p className="text-xs text-muted-foreground mt-2">
										Last synced: {new Date(account.lastSyncAt).toLocaleDateString()}
									</p>
								)}
							</CardContent>
						</Card>
					))}
				</div>
			) : (
				<Card>
					<CardContent className="flex flex-col items-center justify-center py-12">
						<Building2 className="h-12 w-12 text-muted-foreground mb-4" />
						<h3 className="text-lg font-semibold mb-2">No bank accounts connected</h3>
						<p className="text-muted-foreground text-center mb-4">
							Connect your bank accounts to start tracking your finances automatically
						</p>
						<Button onClick={initiateBankConnection} disabled={isLoading}>
							{isLoading ? (
								<Loader2 className="h-4 w-4 animate-spin mr-2" />
							) : (
								<Plus className="h-4 w-4 mr-2" />
							)}
							Connect Your First Bank
						</Button>
					</CardContent>
				</Card>
			)}

			{/* Belvo Connection Form */}
			{showConnectionForm && provider === "belvo" && (
				<Card>
					<CardHeader>
						<CardTitle>Connect Bank Account</CardTitle>
						<CardDescription>
							Select your bank and enter your credentials to connect your account
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<Alert>
							<AlertCircle className="h-4 w-4" />
							<AlertDescription>
								Your credentials are encrypted and securely transmitted. We never store your banking passwords.
							</AlertDescription>
						</Alert>

						<div className="space-y-2">
							<Label htmlFor="institution">Select Bank</Label>
							<Select value={selectedInstitution} onValueChange={setSelectedInstitution}>
								<SelectTrigger>
									<SelectValue placeholder="Choose your bank" />
								</SelectTrigger>
								<SelectContent>
									{institutions.map((institution) => (
										<SelectItem key={institution.id} value={institution.id}>
											{institution.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className="grid grid-cols-2 gap-4">
							<div className="space-y-2">
								<Label htmlFor="username">Username</Label>
								<Input
									id="username"
									type="text"
									value={credentials.username}
									onChange={(e) =>
										setCredentials({ ...credentials, username: e.target.value })
									}
									placeholder="Your bank username"
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="password">Password</Label>
								<Input
									id="password"
									type="password"
									value={credentials.password}
									onChange={(e) =>
										setCredentials({ ...credentials, password: e.target.value })
									}
									placeholder="Your bank password"
								/>
							</div>
						</div>

						{/* Additional fields for some banks */}
						<div className="grid grid-cols-2 gap-4">
							<div className="space-y-2">
								<Label htmlFor="username2">Secondary Username (if required)</Label>
								<Input
									id="username2"
									type="text"
									value={credentials.username2}
									onChange={(e) =>
										setCredentials({ ...credentials, username2: e.target.value })
									}
									placeholder="Optional"
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="username3">Third Username (if required)</Label>
								<Input
									id="username3"
									type="text"
									value={credentials.username3}
									onChange={(e) =>
										setCredentials({ ...credentials, username3: e.target.value })
									}
									placeholder="Optional"
								/>
							</div>
						</div>

						<div className="flex gap-2">
							<Button onClick={completeBelvoConnection} disabled={isLoading}>
								{isLoading ? (
									<Loader2 className="h-4 w-4 animate-spin mr-2" />
								) : null}
								Connect Account
							</Button>
							<Button
								variant="outline"
								onClick={() => setShowConnectionForm(false)}
							>
								Cancel
							</Button>
						</div>
					</CardContent>
				</Card>
			)}
		</div>
	);
}
