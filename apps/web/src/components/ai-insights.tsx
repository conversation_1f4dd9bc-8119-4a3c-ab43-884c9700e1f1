import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
	Brain, 
	TrendingUp, 
	TrendingDown, 
	AlertTriangle, 
	Lightbulb, 
	BarChart3,
	PieChart,
	Target,
	Loader2,
	Eye,
	Archive
} from "lucide-react";
import { toast } from "sonner";
import { orpc } from "@/utils/orpc";

interface SpendingPattern {
	category: string;
	amount: number;
	frequency: number;
	trend: "increasing" | "decreasing" | "stable";
	percentage: number;
}

interface FinancialInsight {
	id: string;
	type: "spending_pattern" | "anomaly" | "recommendation" | "forecast";
	title: string;
	description: string;
	confidence: number;
	data: any;
	actionable: boolean;
	priority: "low" | "medium" | "high";
	isRead: boolean;
	createdAt: string;
}

interface FinancialHealthScore {
	score: number;
	level: string;
	factors: {
		spendingStability: number;
		anomalyCount: number;
		trendAnalysis: Array<{
			category: string;
			trend: string;
			impact: string;
		}>;
	};
}

export function AIInsights() {
	const [isLoading, setIsLoading] = useState(false);
	const [insights, setInsights] = useState<FinancialInsight[]>([]);
	const [patterns, setPatterns] = useState<SpendingPattern[]>([]);
	const [healthScore, setHealthScore] = useState<FinancialHealthScore | null>(null);
	const [activeTab, setActiveTab] = useState("overview");

	useEffect(() => {
		fetchInsights();
		fetchHealthScore();
	}, []);

	const fetchInsights = async () => {
		setIsLoading(true);
		try {
			const response = await orpc.ai.getUserInsights.query({ limit: 20 });
			if (response.success) {
				setInsights(response.insights);
			}
		} catch (error) {
			console.error("Error fetching insights:", error);
			toast.error("Failed to fetch AI insights");
		} finally {
			setIsLoading(false);
		}
	};

	const fetchHealthScore = async () => {
		try {
			const response = await orpc.ai.getFinancialHealthScore.query();
			if (response.success) {
				setHealthScore(response.healthScore);
			}
		} catch (error) {
			console.error("Error fetching health score:", error);
		}
	};

	const generateComprehensiveAnalysis = async () => {
		setIsLoading(true);
		try {
			const response = await orpc.ai.getComprehensiveAnalysis.query();
			if (response.success) {
				setPatterns(response.analysis.patterns);
				// Refresh insights after generating new analysis
				await fetchInsights();
				toast.success("New AI analysis generated!");
			}
		} catch (error) {
			console.error("Error generating analysis:", error);
			toast.error("Failed to generate AI analysis");
		} finally {
			setIsLoading(false);
		}
	};

	const markInsightAsRead = async (insightId: string) => {
		try {
			await orpc.ai.markInsightAsRead.mutate({ insightId });
			setInsights(insights.map(insight => 
				insight.id === insightId ? { ...insight, isRead: true } : insight
			));
		} catch (error) {
			console.error("Error marking insight as read:", error);
		}
	};

	const archiveInsight = async (insightId: string) => {
		try {
			await orpc.ai.archiveInsight.mutate({ insightId });
			setInsights(insights.filter(insight => insight.id !== insightId));
			toast.success("Insight archived");
		} catch (error) {
			console.error("Error archiving insight:", error);
			toast.error("Failed to archive insight");
		}
	};

	const getInsightIcon = (type: string) => {
		switch (type) {
			case "anomaly":
				return <AlertTriangle className="h-5 w-5 text-orange-500" />;
			case "recommendation":
				return <Lightbulb className="h-5 w-5 text-blue-500" />;
			case "forecast":
				return <TrendingUp className="h-5 w-5 text-green-500" />;
			default:
				return <BarChart3 className="h-5 w-5 text-purple-500" />;
		}
	};

	const getPriorityColor = (priority: string) => {
		switch (priority) {
			case "high":
				return "destructive";
			case "medium":
				return "default";
			default:
				return "secondary";
		}
	};

	const getTrendIcon = (trend: string) => {
		switch (trend) {
			case "increasing":
				return <TrendingUp className="h-4 w-4 text-red-500" />;
			case "decreasing":
				return <TrendingDown className="h-4 w-4 text-green-500" />;
			default:
				return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
		}
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: "USD",
		}).format(amount);
	};

	if (isLoading && insights.length === 0) {
		return (
			<div className="flex items-center justify-center py-12">
				<Loader2 className="h-8 w-8 animate-spin" />
				<span className="ml-2">Loading AI insights...</span>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h2 className="text-2xl font-bold flex items-center gap-2">
						<Brain className="h-6 w-6" />
						AI Financial Insights
					</h2>
					<p className="text-muted-foreground">
						Personalized insights powered by artificial intelligence
					</p>
				</div>
				<Button onClick={generateComprehensiveAnalysis} disabled={isLoading}>
					{isLoading ? (
						<Loader2 className="h-4 w-4 animate-spin mr-2" />
					) : (
						<Brain className="h-4 w-4 mr-2" />
					)}
					Generate New Analysis
				</Button>
			</div>

			{/* Financial Health Score */}
			{healthScore && (
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Target className="h-5 w-5" />
							Financial Health Score
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="flex items-center gap-4">
							<div className="text-4xl font-bold text-primary">
								{healthScore.score}
							</div>
							<div>
								<Badge variant={
									healthScore.level === "Excellent" ? "default" :
									healthScore.level === "Good" ? "secondary" :
									healthScore.level === "Fair" ? "outline" : "destructive"
								}>
									{healthScore.level}
								</Badge>
								<p className="text-sm text-muted-foreground mt-1">
									Based on spending patterns and financial behavior
								</p>
							</div>
						</div>
					</CardContent>
				</Card>
			)}

			<Tabs value={activeTab} onValueChange={setActiveTab}>
				<TabsList className="grid w-full grid-cols-4">
					<TabsTrigger value="overview">Overview</TabsTrigger>
					<TabsTrigger value="patterns">Spending Patterns</TabsTrigger>
					<TabsTrigger value="insights">Insights</TabsTrigger>
					<TabsTrigger value="recommendations">Recommendations</TabsTrigger>
				</TabsList>

				<TabsContent value="overview" className="space-y-4">
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
						{insights.slice(0, 6).map((insight) => (
							<Card key={insight.id} className={!insight.isRead ? "border-primary" : ""}>
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<CardTitle className="text-sm font-medium flex items-center gap-2">
										{getInsightIcon(insight.type)}
										{insight.title}
									</CardTitle>
									<Badge variant={getPriorityColor(insight.priority)}>
										{insight.priority}
									</Badge>
								</CardHeader>
								<CardContent>
									<p className="text-sm text-muted-foreground mb-3">
										{insight.description}
									</p>
									<div className="flex items-center justify-between">
										<span className="text-xs text-muted-foreground">
											Confidence: {Math.round(insight.confidence * 100)}%
										</span>
										<div className="flex gap-1">
											{!insight.isRead && (
												<Button
													size="sm"
													variant="ghost"
													onClick={() => markInsightAsRead(insight.id)}
												>
													<Eye className="h-3 w-3" />
												</Button>
											)}
											<Button
												size="sm"
												variant="ghost"
												onClick={() => archiveInsight(insight.id)}
											>
												<Archive className="h-3 w-3" />
											</Button>
										</div>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</TabsContent>

				<TabsContent value="patterns" className="space-y-4">
					{patterns.length > 0 ? (
						<div className="space-y-4">
							{patterns.slice(0, 10).map((pattern, index) => (
								<Card key={index}>
									<CardContent className="pt-6">
										<div className="flex items-center justify-between">
											<div className="flex items-center gap-3">
												<PieChart className="h-5 w-5 text-muted-foreground" />
												<div>
													<h3 className="font-medium">{pattern.category}</h3>
													<p className="text-sm text-muted-foreground">
														{pattern.frequency} transactions
													</p>
												</div>
											</div>
											<div className="text-right">
												<div className="flex items-center gap-2">
													<span className="font-semibold">
														{formatCurrency(pattern.amount)}
													</span>
													{getTrendIcon(pattern.trend)}
												</div>
												<p className="text-sm text-muted-foreground">
													{pattern.percentage.toFixed(1)}% of spending
												</p>
											</div>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					) : (
						<Alert>
							<AlertTriangle className="h-4 w-4" />
							<AlertDescription>
								No spending patterns available. Generate a new analysis to see your spending patterns.
							</AlertDescription>
						</Alert>
					)}
				</TabsContent>

				<TabsContent value="insights" className="space-y-4">
					{insights.filter(i => i.type === "anomaly" || i.type === "spending_pattern").map((insight) => (
						<Card key={insight.id} className={!insight.isRead ? "border-primary" : ""}>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									{getInsightIcon(insight.type)}
									{insight.title}
									<Badge variant={getPriorityColor(insight.priority)}>
										{insight.priority}
									</Badge>
								</CardTitle>
								<CardDescription>
									Generated on {new Date(insight.createdAt).toLocaleDateString()}
								</CardDescription>
							</CardHeader>
							<CardContent>
								<p className="mb-4">{insight.description}</p>
								<div className="flex items-center justify-between">
									<span className="text-sm text-muted-foreground">
										Confidence: {Math.round(insight.confidence * 100)}%
									</span>
									<div className="flex gap-2">
										{!insight.isRead && (
											<Button
												size="sm"
												variant="outline"
												onClick={() => markInsightAsRead(insight.id)}
											>
												<Eye className="h-4 w-4 mr-1" />
												Mark as Read
											</Button>
										)}
										<Button
											size="sm"
											variant="ghost"
											onClick={() => archiveInsight(insight.id)}
										>
											<Archive className="h-4 w-4 mr-1" />
											Archive
										</Button>
									</div>
								</div>
							</CardContent>
						</Card>
					))}
				</TabsContent>

				<TabsContent value="recommendations" className="space-y-4">
					{insights.filter(i => i.type === "recommendation").map((insight) => (
						<Card key={insight.id} className={!insight.isRead ? "border-primary" : ""}>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<Lightbulb className="h-5 w-5 text-blue-500" />
									{insight.title}
									<Badge variant={getPriorityColor(insight.priority)}>
										{insight.priority}
									</Badge>
								</CardTitle>
							</CardHeader>
							<CardContent>
								<p className="mb-4">{insight.description}</p>
								{insight.actionable && (
									<Alert>
										<Lightbulb className="h-4 w-4" />
										<AlertDescription>
											This recommendation includes actionable steps you can take to improve your financial health.
										</AlertDescription>
									</Alert>
								)}
								<div className="flex items-center justify-between mt-4">
									<span className="text-sm text-muted-foreground">
										Confidence: {Math.round(insight.confidence * 100)}%
									</span>
									<div className="flex gap-2">
										{!insight.isRead && (
											<Button
												size="sm"
												variant="outline"
												onClick={() => markInsightAsRead(insight.id)}
											>
												<Eye className="h-4 w-4 mr-1" />
												Mark as Read
											</Button>
										)}
										<Button
											size="sm"
											variant="ghost"
											onClick={() => archiveInsight(insight.id)}
										>
											<Archive className="h-4 w-4 mr-1" />
											Archive
										</Button>
									</div>
								</div>
							</CardContent>
						</Card>
					))}
				</TabsContent>
			</Tabs>
		</div>
	);
}
