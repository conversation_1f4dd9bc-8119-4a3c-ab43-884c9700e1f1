import { z } from "zod";
import { protectedProcedure } from "../lib/orpc";
import { BankingService } from "../services/banking";

const bankingService = new BankingService();

// Input schemas
const initiateBankConnectionSchema = z.object({
	provider: z.enum(["plaid", "belvo"]),
	countryCode: z.string().optional(),
});

const completeBankConnectionSchema = z.object({
	provider: z.enum(["plaid", "belvo"]),
	connectionData: z.record(z.any()),
});

const syncAccountsSchema = z.object({
	provider: z.enum(["plaid", "belvo"]),
	connectionId: z.string(),
	accessToken: z.string().optional(),
});

const syncTransactionsSchema = z.object({
	provider: z.enum(["plaid", "belvo"]),
	connectionId: z.string(),
	dateFrom: z.string().datetime(),
	dateTo: z.string().datetime(),
	accessToken: z.string().optional(),
});

const getUserTransactionsSchema = z.object({
	dateFrom: z.string().datetime().optional(),
	dateTo: z.string().datetime().optional(),
	accountId: z.string().optional(),
	limit: z.number().min(1).max(1000).default(100),
	offset: z.number().min(0).default(0),
});

const disconnectBankSchema = z.object({
	provider: z.enum(["plaid", "belvo"]),
	connectionId: z.string(),
	accessToken: z.string().optional(),
});

export const bankingRouter = {
	// Get recommended provider based on user location
	getRecommendedProvider: protectedProcedure
		.input(z.object({ countryCode: z.string() }))
		.handler(async ({ input }) => {
			const provider = bankingService.getRecommendedProvider(input.countryCode);
			return { provider };
		}),

	// Initiate bank connection (get link token for Plaid or institutions for Belvo)
	initiateBankConnection: protectedProcedure
		.input(initiateBankConnectionSchema)
		.handler(async ({ input, context }) => {
			const userId = context.session?.user?.id;
			if (!userId) {
				throw new Error("User not authenticated");
			}

			const result = await bankingService.initiateBankConnection(
				userId,
				input.provider,
				input.countryCode,
			);

			return {
				success: true,
				data: result,
				provider: input.provider,
			};
		}),

	// Complete bank connection after user authentication
	completeBankConnection: protectedProcedure
		.input(completeBankConnectionSchema)
		.handler(async ({ input, context }) => {
			const userId = context.session?.user?.id;
			if (!userId) {
				throw new Error("User not authenticated");
			}

			const connection = await bankingService.completeBankConnection(
				userId,
				input.provider,
				input.connectionData,
			);

			return {
				success: true,
				connection,
				message: "Bank connection completed successfully",
			};
		}),

	// Get user's bank accounts
	getUserBankAccounts: protectedProcedure.handler(async ({ context }) => {
		const userId = context.session?.user?.id;
		if (!userId) {
			throw new Error("User not authenticated");
		}

		const accounts = await bankingService.getUserBankAccounts(userId);
		return {
			success: true,
			accounts,
		};
	}),

	// Get user's balance summary
	getUserBalanceSummary: protectedProcedure.handler(async ({ context }) => {
		const userId = context.session?.user?.id;
		if (!userId) {
			throw new Error("User not authenticated");
		}

		const summary = await bankingService.getUserBalanceSummary(userId);
		return {
			success: true,
			summary,
		};
	}),

	// Sync accounts manually
	syncAccounts: protectedProcedure
		.input(syncAccountsSchema)
		.handler(async ({ input, context }) => {
			const userId = context.session?.user?.id;
			if (!userId) {
				throw new Error("User not authenticated");
			}

			const result = await bankingService.syncUserAccounts(
				userId,
				input.provider,
				input.connectionId,
				input.accessToken,
			);

			return {
				success: true,
				result,
				message: "Accounts synced successfully",
			};
		}),

	// Sync transactions manually
	syncTransactions: protectedProcedure
		.input(syncTransactionsSchema)
		.handler(async ({ input, context }) => {
			const userId = context.session?.user?.id;
			if (!userId) {
				throw new Error("User not authenticated");
			}

			const result = await bankingService.syncUserTransactions(
				userId,
				input.provider,
				input.connectionId,
				new Date(input.dateFrom),
				new Date(input.dateTo),
				input.accessToken,
			);

			return {
				success: true,
				result,
				message: "Transactions synced successfully",
			};
		}),

	// Get user transactions
	getUserTransactions: protectedProcedure
		.input(getUserTransactionsSchema)
		.handler(async ({ input, context }) => {
			const userId = context.session?.user?.id;
			if (!userId) {
				throw new Error("User not authenticated");
			}

			const transactions = await bankingService.getUserTransactions(
				userId,
				input.dateFrom ? new Date(input.dateFrom) : undefined,
				input.dateTo ? new Date(input.dateTo) : undefined,
				input.accountId,
				input.limit,
				input.offset,
			);

			return {
				success: true,
				transactions,
				pagination: {
					limit: input.limit,
					offset: input.offset,
					total: transactions.length,
				},
			};
		}),

	// Disconnect bank
	disconnectBank: protectedProcedure
		.input(disconnectBankSchema)
		.handler(async ({ input, context }) => {
			const userId = context.session?.user?.id;
			if (!userId) {
				throw new Error("User not authenticated");
			}

			const success = await bankingService.disconnectBank(
				userId,
				input.provider,
				input.connectionId,
				input.accessToken,
			);

			return {
				success,
				message: success
					? "Bank disconnected successfully"
					: "Failed to disconnect bank",
			};
		}),

	// Get account details
	getAccountDetails: protectedProcedure
		.input(z.object({ accountId: z.string() }))
		.handler(async ({ input, context }) => {
			const userId = context.session?.user?.id;
			if (!userId) {
				throw new Error("User not authenticated");
			}

			// Get account details with recent transactions
			const accounts = await bankingService.getUserBankAccounts(userId);
			const account = accounts.find((a) => a.id === input.accountId);

			if (!account) {
				throw new Error("Account not found");
			}

			// Get recent transactions for this account
			const recentTransactions = await bankingService.getUserTransactions(
				userId,
				undefined,
				undefined,
				input.accountId,
				10,
				0,
			);

			return {
				success: true,
				account,
				recentTransactions,
			};
		}),

	// Get transaction categories for categorization
	getTransactionCategories: protectedProcedure.handler(async () => {
		// This would typically fetch from the database
		// For now, return some default categories
		const categories = [
			{ id: "1", name: "Food & Dining", icon: "🍽️", color: "#EF4444" },
			{ id: "2", name: "Transportation", icon: "🚗", color: "#F59E0B" },
			{ id: "3", name: "Shopping", icon: "🛍️", color: "#8B5CF6" },
			{ id: "4", name: "Entertainment", icon: "🎬", color: "#EC4899" },
			{ id: "5", name: "Bills & Utilities", icon: "⚡", color: "#6B7280" },
			{ id: "6", name: "Healthcare", icon: "🏥", color: "#EF4444" },
			{ id: "7", name: "Income", icon: "💰", color: "#10B981" },
		];

		return {
			success: true,
			categories,
		};
	}),

	// Update transaction category
	updateTransactionCategory: protectedProcedure
		.input(
			z.object({
				transactionId: z.string(),
				categoryId: z.string(),
			}),
		)
		.handler(async ({ input, context }) => {
			const userId = context.session?.user?.id;
			if (!userId) {
				throw new Error("User not authenticated");
			}

			try {
				// Update the transaction category in the database
				const { db } = await import("../db");
				const { transactions } = await import("../db/schema/financial");
				const { eq, and } = await import("drizzle-orm");

				const [updatedTransaction] = await db
					.update(transactions)
					.set({
						categoryId: input.categoryId,
						updatedAt: new Date(),
					})
					.where(
						and(
							eq(transactions.id, input.transactionId),
							eq(transactions.userId, userId),
						),
					)
					.returning();

				if (!updatedTransaction) {
					throw new Error("Transaction not found or access denied");
				}

				return {
					success: true,
					message: "Transaction category updated successfully",
					transaction: updatedTransaction,
				};
			} catch (error) {
				console.error("Error updating transaction category:", error);
				throw new Error("Failed to update transaction category");
			}
		}),

	// Get spending insights
	getSpendingInsights: protectedProcedure
		.input(
			z.object({
				period: z.enum(["week", "month", "quarter", "year"]).default("month"),
			}),
		)
		.handler(async ({ input, context }) => {
			const userId = context.session?.user?.id;
			if (!userId) {
				throw new Error("User not authenticated");
			}

			// Calculate date range based on period
			const now = new Date();
			const dateFrom = new Date();

			switch (input.period) {
				case "week":
					dateFrom.setDate(now.getDate() - 7);
					break;
				case "month":
					dateFrom.setMonth(now.getMonth() - 1);
					break;
				case "quarter":
					dateFrom.setMonth(now.getMonth() - 3);
					break;
				case "year":
					dateFrom.setFullYear(now.getFullYear() - 1);
					break;
			}

			const transactions = await bankingService.getUserTransactions(
				userId,
				dateFrom,
				now,
				undefined,
				1000,
				0,
			);

			// Calculate insights
			const totalSpent = transactions
				.filter((t) => t.type === "expense")
				.reduce((sum, t) => sum + Number.parseFloat(t.amount), 0);

			const totalIncome = transactions
				.filter((t) => t.type === "income")
				.reduce((sum, t) => sum + Number.parseFloat(t.amount), 0);

			const categorySpending = transactions
				.filter((t) => t.type === "expense")
				.reduce(
					(acc, t) => {
						const category = t.categoryId || "uncategorized";
						acc[category] = (acc[category] || 0) + Number.parseFloat(t.amount);
						return acc;
					},
					{} as Record<string, number>,
				);

			return {
				success: true,
				insights: {
					period: input.period,
					totalSpent,
					totalIncome,
					netIncome: totalIncome - totalSpent,
					transactionCount: transactions.length,
					categorySpending,
					averageTransactionAmount:
						transactions.length > 0
							? (totalSpent + totalIncome) / transactions.length
							: 0,
				},
			};
		}),
};
