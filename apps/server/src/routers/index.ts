import { protectedProcedure, publicProcedure } from "../lib/orpc";
import { aiRouter } from "./ai";
import { bankingRouter } from "./banking";

export const appRouter = {
	healthCheck: publicProcedure.handler(() => {
		return "OK";
	}),
	privateData: protectedProcedure.handler(({ context }) => {
		return {
			message: "This is private",
			user: context.session?.user,
		};
	}),
	banking: bankingRouter,
	ai: aiRouter,
};
export type AppRouter = typeof appRouter;
