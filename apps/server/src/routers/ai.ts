import { z } from "zod";
import { protectedProcedure } from "../lib/orpc";
import { AIService } from "../services/ai";

const aiService = new AIService();

// Input schemas
const analyzeSpendingPatternsSchema = z.object({
	months: z.number().min(1).max(24).default(3),
});

const generateForecastSchema = z.object({
	months: z.number().min(1).max(12).default(3),
});

const categorizeTransactionSchema = z.object({
	description: z.string(),
	amount: z.number(),
	merchantName: z.string().optional(),
});

const getUserInsightsSchema = z.object({
	limit: z.number().min(1).max(50).default(10),
});

export const aiRouter = {
	// Analyze spending patterns
	analyzeSpendingPatterns: protectedProcedure
		.input(analyzeSpendingPatternsSchema)
		.handler(async ({ input, context }) => {
			const userId = context.session?.user?.id;
			if (!userId) {
				throw new Error("User not authenticated");
			}

			const patterns = await aiService.analyzeSpendingPatterns(userId, input.months);

			return {
				success: true,
				patterns,
				period: `${input.months} months`,
			};
		}),

	// Detect spending anomalies
	detectAnomalies: protectedProcedure.handler(async ({ context }) => {
		const userId = context.session?.user?.id;
		if (!userId) {
			throw new Error("User not authenticated");
		}

		const anomalies = await aiService.detectAnomalies(userId);

		return {
			success: true,
			anomalies,
			count: anomalies.length,
		};
	}),

	// Generate personalized recommendations
	generateRecommendations: protectedProcedure.handler(async ({ context }) => {
		const userId = context.session?.user?.id;
		if (!userId) {
			throw new Error("User not authenticated");
		}

		const recommendations = await aiService.generateRecommendations(userId);

		return {
			success: true,
			recommendations,
			count: recommendations.length,
		};
	}),

	// Generate spending forecast
	generateForecast: protectedProcedure
		.input(generateForecastSchema)
		.handler(async ({ input, context }) => {
			const userId = context.session?.user?.id;
			if (!userId) {
				throw new Error("User not authenticated");
			}

			const forecast = await aiService.generateSpendingForecast(userId, input.months);

			return {
				success: true,
				forecast,
			};
		}),

	// Get comprehensive financial analysis
	getComprehensiveAnalysis: protectedProcedure.handler(async ({ context }) => {
		const userId = context.session?.user?.id;
		if (!userId) {
			throw new Error("User not authenticated");
		}

		const analysis = await aiService.generateComprehensiveAnalysis(userId);

		return {
			success: true,
			analysis,
			summary: {
				totalPatterns: analysis.patterns.length,
				totalAnomalies: analysis.anomalies.length,
				totalRecommendations: analysis.recommendations.length,
				forecastPeriod: "3 months",
			},
		};
	}),

	// Get user insights from database
	getUserInsights: protectedProcedure
		.input(getUserInsightsSchema)
		.handler(async ({ input, context }) => {
			const userId = context.session?.user?.id;
			if (!userId) {
				throw new Error("User not authenticated");
			}

			const insights = await aiService.getUserInsights(userId, input.limit);

			return {
				success: true,
				insights,
				count: insights.length,
			};
		}),

	// Categorize a transaction using AI
	categorizeTransaction: protectedProcedure
		.input(categorizeTransactionSchema)
		.handler(async ({ input }) => {
			const category = await aiService.categorizeTransaction(
				input.description,
				input.amount,
				input.merchantName,
			);

			return {
				success: true,
				category,
				confidence: 0.8, // Placeholder confidence score
			};
		}),

	// Get spending insights by category
	getCategoryInsights: protectedProcedure
		.input(z.object({ months: z.number().min(1).max(12).default(3) }))
		.handler(async ({ input, context }) => {
			const userId = context.session?.user?.id;
			if (!userId) {
				throw new Error("User not authenticated");
			}

			const patterns = await aiService.analyzeSpendingPatterns(userId, input.months);

			// Calculate additional insights
			const totalSpending = patterns.reduce((sum, p) => sum + p.amount, 0);
			const topCategories = patterns.slice(0, 5);
			const categoryTrends = patterns.map((p) => ({
				category: p.category,
				trend: p.trend,
				percentage: p.percentage,
				amount: p.amount,
			}));

			return {
				success: true,
				insights: {
					totalSpending,
					topCategories,
					categoryTrends,
					period: `${input.months} months`,
				},
			};
		}),

	// Get financial health score
	getFinancialHealthScore: protectedProcedure.handler(async ({ context }) => {
		const userId = context.session?.user?.id;
		if (!userId) {
			throw new Error("User not authenticated");
		}

		try {
			const patterns = await aiService.analyzeSpendingPatterns(userId, 3);
			const anomalies = await aiService.detectAnomalies(userId);

			// Calculate a simple financial health score (0-100)
			let score = 100;

			// Deduct points for high spending in volatile categories
			const volatileSpending = patterns.filter((p) => p.trend === "increasing").length;
			score -= volatileSpending * 5;

			// Deduct points for anomalies
			score -= anomalies.length * 10;

			// Ensure score is between 0 and 100
			score = Math.max(0, Math.min(100, score));

			let healthLevel = "Excellent";
			if (score < 70) healthLevel = "Good";
			if (score < 50) healthLevel = "Fair";
			if (score < 30) healthLevel = "Poor";

			return {
				success: true,
				healthScore: {
					score,
					level: healthLevel,
					factors: {
						spendingStability: 100 - volatileSpending * 5,
						anomalyCount: anomalies.length,
						trendAnalysis: patterns.map((p) => ({
							category: p.category,
							trend: p.trend,
							impact: p.percentage > 20 ? "high" : p.percentage > 10 ? "medium" : "low",
						})),
					},
				},
			};
		} catch (error) {
			console.error("Error calculating financial health score:", error);
			throw new Error("Failed to calculate financial health score");
		}
	}),

	// Get monthly spending comparison
	getMonthlyComparison: protectedProcedure.handler(async ({ context }) => {
		const userId = context.session?.user?.id;
		if (!userId) {
			throw new Error("User not authenticated");
		}

		try {
			// Get current month and previous month patterns
			const currentMonthPatterns = await aiService.analyzeSpendingPatterns(userId, 1);
			const previousMonthPatterns = await aiService.analyzeSpendingPatterns(userId, 2);

			const currentTotal = currentMonthPatterns.reduce((sum, p) => sum + p.amount, 0);
			const previousTotal = previousMonthPatterns.reduce((sum, p) => sum + p.amount, 0) / 2; // Average of 2 months

			const changePercent = ((currentTotal - previousTotal) / previousTotal) * 100;

			// Category-wise comparison
			const categoryComparison = currentMonthPatterns.map((current) => {
				const previous = previousMonthPatterns.find((p) => p.category === current.category);
				const previousAmount = previous ? previous.amount / 2 : 0; // Average of 2 months
				const change = previousAmount > 0 ? ((current.amount - previousAmount) / previousAmount) * 100 : 0;

				return {
					category: current.category,
					currentAmount: current.amount,
					previousAmount,
					change,
					trend: change > 10 ? "increasing" : change < -10 ? "decreasing" : "stable",
				};
			});

			return {
				success: true,
				comparison: {
					currentMonth: currentTotal,
					previousMonth: previousTotal,
					changePercent,
					trend: changePercent > 10 ? "increasing" : changePercent < -10 ? "decreasing" : "stable",
					categoryComparison,
				},
			};
		} catch (error) {
			console.error("Error generating monthly comparison:", error);
			throw new Error("Failed to generate monthly comparison");
		}
	}),

	// Mark insight as read
	markInsightAsRead: protectedProcedure
		.input(z.object({ insightId: z.string() }))
		.handler(async ({ input, context }) => {
			const userId = context.session?.user?.id;
			if (!userId) {
				throw new Error("User not authenticated");
			}

			// This would update the insight in the database
			// For now, just return success
			return {
				success: true,
				message: "Insight marked as read",
			};
		}),

	// Archive insight
	archiveInsight: protectedProcedure
		.input(z.object({ insightId: z.string() }))
		.handler(async ({ input, context }) => {
			const userId = context.session?.user?.id;
			if (!userId) {
				throw new Error("User not authenticated");
			}

			// This would archive the insight in the database
			// For now, just return success
			return {
				success: true,
				message: "Insight archived",
			};
		}),
};
