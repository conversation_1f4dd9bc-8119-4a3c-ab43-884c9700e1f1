import Client from "belvo";
import { and, eq } from "drizzle-orm";
import { db } from "../db";
import {
	bankAccounts,
	financialInstitutions,
	transactions,
} from "../db/schema/financial";

export class BelvoService {
	private client: Client;

	constructor() {
		const environment = process.env.BELVO_ENV || "sandbox";
		this.client = new Client(
			process.env.BELVO_SECRET_ID || "",
			process.env.BELVO_SECRET_PASSWORD || "",
			environment,
		);
	}

	/**
	 * Get available institutions
	 */
	async getInstitutions(country?: string) {
		try {
			// Note: Belvo API doesn't support country filtering in list method
			// You would need to filter the results after fetching
			const institutions = await this.client.institutions.list();

			if (country) {
				return institutions.filter(
					(inst) =>
						inst.country_code === country ||
						inst.country_codes?.includes(country),
				);
			}

			return institutions;
		} catch (error) {
			console.error("Error fetching institutions:", error);
			throw new Error("Failed to fetch institutions");
		}
	}

	/**
	 * Create a link (register user credentials with institution)
	 */
	async createLink(
		institution: string,
		username: string,
		password: string,
		username2?: string,
		username3?: string,
	) {
		try {
			const options = {
				...(username2 && { username2 }),
				...(username3 && { username3 }),
			};

			const link = await this.client.links.register(
				institution,
				username,
				password,
				Object.keys(options).length > 0 ? options : undefined,
			);
			return link;
		} catch (error) {
			console.error("Error creating link:", error);
			throw new Error("Failed to create link");
		}
	}

	/**
	 * Get accounts for a link
	 */
	async getAccounts(linkId: string) {
		try {
			const accounts = await this.client.accounts.retrieve(linkId);
			return accounts;
		} catch (error) {
			console.error("Error fetching accounts:", error);
			throw new Error("Failed to fetch accounts");
		}
	}

	/**
	 * Sync accounts to database
	 */
	async syncAccountsToDatabase(userId: string, linkId: string) {
		try {
			const accountsData = await this.getAccounts(linkId);

			// Get link details to find institution
			const linkDetails = await this.client.links.detail(linkId);
			let institutionId: string | null = null;

			if (linkDetails.institution) {
				// Check if institution exists in our database
				const [existingInstitution] = await db
					.select()
					.from(financialInstitutions)
					.where(
						eq(
							financialInstitutions.belvoInstitutionId,
							linkDetails.institution,
						),
					);

				if (!existingInstitution) {
					// Get institution details from Belvo
					const institutionDetails = await this.client.institutions.detail(
						linkDetails.institution,
					);

					const [newInstitution] = await db
						.insert(financialInstitutions)
						.values({
							name: institutionDetails.display_name,
							logo: institutionDetails.logo || null,
							website: institutionDetails.website || null,
							belvoInstitutionId: institutionDetails.name,
							supportedCountries: [institutionDetails.country_code],
						})
						.returning({ id: financialInstitutions.id });
					institutionId = newInstitution.id;
				} else {
					institutionId = existingInstitution.id;
				}
			}

			// Sync accounts
			const syncedAccounts = [];
			for (const account of accountsData) {
				const accountType = this.mapBelvoAccountType(
					account.type,
					account.category,
				);

				// Skip accounts without IDs
				if (!account.id) {
					console.warn("Account missing ID, skipping");
					continue;
				}

				const [existingAccount] = await db
					.select()
					.from(bankAccounts)
					.where(
						and(
							eq(bankAccounts.userId, userId),
							eq(bankAccounts.belvoAccountId, account.id),
						),
					);

				if (existingAccount) {
					// Update existing account
					const [updatedAccount] = await db
						.update(bankAccounts)
						.set({
							balance: account.balance.current?.toString() || "0.00",
							availableBalance: account.balance.available?.toString() || "0.00",
							lastSyncAt: new Date(),
							updatedAt: new Date(),
						})
						.where(eq(bankAccounts.id, existingAccount.id))
						.returning();
					syncedAccounts.push(updatedAccount);
				} else {
					// Create new account
					const [newAccount] = await db
						.insert(bankAccounts)
						.values({
							userId,
							institutionId,
							accountType,
							accountName: account.name || account.number,
							accountNumber: account.number || null,
							balance: account.balance.current?.toString() || "0.00",
							availableBalance: account.balance.available?.toString() || "0.00",
							currency: account.currency,
							belvoAccountId: account.id,
							lastSyncAt: new Date(),
							metadata: {
								linkId,
								category: account.category,
								publicIdentificationName: account.public_identification_name,
								publicIdentificationValue: account.public_identification_value,
							},
						})
						.returning();
					syncedAccounts.push(newAccount);
				}
			}

			return syncedAccounts;
		} catch (error) {
			console.error("Error syncing accounts:", error);
			throw new Error("Failed to sync accounts");
		}
	}

	/**
	 * Get transactions for an account
	 */
	async getTransactions(
		linkId: string,
		dateFrom: string,
		dateTo: string,
		accountId?: string,
	) {
		try {
			const options = {
				...(accountId && { account: accountId }),
			};

			const transactions = await this.client.transactions.retrieve(
				linkId,
				dateFrom,
				dateTo,
				Object.keys(options).length > 0 ? options : undefined,
			);
			return transactions;
		} catch (error) {
			console.error("Error fetching transactions:", error);
			throw new Error("Failed to fetch transactions");
		}
	}

	/**
	 * Sync transactions to database
	 */
	async syncTransactionsToDatabase(
		userId: string,
		linkId: string,
		dateFrom: string,
		dateTo: string,
	) {
		try {
			const transactionsData = await this.getTransactions(
				linkId,
				dateFrom,
				dateTo,
			);

			const syncedTransactions = [];
			for (const transaction of transactionsData) {
				// Skip transactions without IDs
				if (!transaction.id || !transaction.account?.id) {
					console.warn("Transaction missing ID or account ID, skipping");
					continue;
				}

				// Find the corresponding account in our database
				const [account] = await db
					.select()
					.from(bankAccounts)
					.where(
						and(
							eq(bankAccounts.userId, userId),
							eq(bankAccounts.belvoAccountId, transaction.account.id),
						),
					);

				if (!account) {
					console.warn(`Account not found for transaction ${transaction.id}`);
					continue;
				}

				// Check if transaction already exists
				const [existingTransaction] = await db
					.select()
					.from(transactions)
					.where(eq(transactions.belvoTransactionId, transaction.id));

				if (!existingTransaction) {
					const transactionType =
						transaction.amount >= 0 ? "income" : "expense";

					const [newTransaction] = await db
						.insert(transactions)
						.values({
							userId,
							accountId: account.id,
							type: transactionType,
							amount: Math.abs(transaction.amount).toString(),
							currency: transaction.currency,
							description: transaction.description || "Transaction",
							merchantName: null, // Belvo doesn't provide merchant info in the same way
							date: new Date(transaction.value_date),
							authorizedDate: new Date(transaction.accounting_date),
							belvoTransactionId: transaction.id,
							metadata: {
								reference: transaction.reference,
								category: transaction.category,
								creditCardData: transaction.credit_card_data,
								// Store additional transaction data
								internalIdentification: transaction.internal_identification,
								observations: transaction.observations,
								type: transaction.type,
								status: transaction.status,
							},
						})
						.returning();
					syncedTransactions.push(newTransaction);
				}
			}

			return syncedTransactions;
		} catch (error) {
			console.error("Error syncing transactions:", error);
			throw new Error("Failed to sync transactions");
		}
	}

	/**
	 * Map Belvo account types to our internal types
	 */
	private mapBelvoAccountType(
		_type: string,
		category: string,
	):
		| "checking"
		| "savings"
		| "credit_card"
		| "investment"
		| "loan"
		| "mortgage"
		| "other" {
		// Belvo account types mapping based on category
		switch (category?.toLowerCase()) {
			case "checking_account":
			case "current_account":
				return "checking";
			case "savings_account":
				return "savings";
			case "credit_card":
				return "credit_card";
			case "investment_account":
			case "pension_fund_account":
				return "investment";
			case "loan_account":
				return "loan";
			case "mortgage_account":
				return "mortgage";
			default:
				return "other";
		}
	}

	/**
	 * Delete a link (disconnect bank)
	 */
	async deleteLink(linkId: string) {
		try {
			await this.client.links.delete(linkId);
			return true;
		} catch (error) {
			console.error("Error deleting link:", error);
			throw new Error("Failed to delete link");
		}
	}

	/**
	 * Get link status
	 */
	async getLinkStatus(linkId: string) {
		try {
			const link = await this.client.links.detail(linkId);
			return link;
		} catch (error) {
			console.error("Error getting link status:", error);
			throw new Error("Failed to get link status");
		}
	}

	/**
	 * Refresh link (update credentials)
	 */
	async refreshLink(linkId: string, username?: string, password?: string) {
		try {
			const updateData: { username?: string; password?: string } = {};
			if (username) updateData.username = username;
			if (password) updateData.password = password;

			const link = await this.client.links.update(linkId, updateData);
			return link;
		} catch (error) {
			console.error("Error refreshing link:", error);
			throw new Error("Failed to refresh link");
		}
	}

	/**
	 * Get account balances
	 */
	async getBalances(linkId: string, dateFrom: string, accountId?: string) {
		try {
			const options = {
				...(accountId && { account: accountId }),
			};

			const balances = await this.client.balances.retrieve(
				linkId,
				dateFrom,
				Object.keys(options).length > 0 ? options : undefined,
			);
			return balances;
		} catch (error) {
			console.error("Error fetching balances:", error);
			throw new Error("Failed to fetch balances");
		}
	}
}
