import { PlaidService } from "./plaid";
import { BelvoService } from "./belvo";
import { db } from "../db";
import { bankAccounts, transactions } from "../db/schema/financial";
import { eq, and, gte, lte } from "drizzle-orm";
import cron from "node-cron";

export type BankingProvider = "plaid" | "belvo";

export interface BankConnection {
	id: string;
	provider: BankingProvider;
	accessToken?: string;
	linkId?: string;
	institutionId: string;
	userId: string;
	isActive: boolean;
	lastSyncAt?: Date;
	metadata?: Record<string, any>;
}

export interface SyncResult {
	accountsAdded: number;
	accountsUpdated: number;
	transactionsAdded: number;
	errors: string[];
}

export class BankingService {
	private plaidService: PlaidService;
	private belvoService: BelvoService;

	constructor() {
		this.plaidService = new PlaidService();
		this.belvoService = new BelvoService();
		this.initializeScheduledSync();
	}

	/**
	 * Determine the appropriate provider based on user location or preference
	 */
	getRecommendedProvider(countryCode: string): BankingProvider {
		const latamCountries = ["MX", "CO", "BR", "AR", "CL", "PE", "UY"];
		return latamCountries.includes(countryCode) ? "belvo" : "plaid";
	}

	/**
	 * Create a link token for Plaid or get institutions for Belvo
	 */
	async initiateBankConnection(
		userId: string,
		provider: BankingProvider,
		countryCode?: string,
	) {
		try {
			if (provider === "plaid") {
				return await this.plaidService.createLinkToken(userId);
			} else {
				return await this.belvoService.getInstitutions(countryCode);
			}
		} catch (error) {
			console.error(`Error initiating ${provider} connection:`, error);
			throw new Error(`Failed to initiate ${provider} connection`);
		}
	}

	/**
	 * Complete bank connection after user authentication
	 */
	async completeBankConnection(
		userId: string,
		provider: BankingProvider,
		connectionData: any,
	): Promise<BankConnection> {
		try {
			if (provider === "plaid") {
				const { publicToken } = connectionData;
				const exchangeResult = await this.plaidService.exchangePublicToken(
					publicToken,
				);

				// Sync accounts immediately
				await this.plaidService.syncAccountsToDatabase(
					userId,
					exchangeResult.access_token,
					exchangeResult.item_id,
				);

				return {
					id: exchangeResult.item_id,
					provider: "plaid",
					accessToken: exchangeResult.access_token,
					institutionId: exchangeResult.item_id,
					userId,
					isActive: true,
					lastSyncAt: new Date(),
					metadata: {
						itemId: exchangeResult.item_id,
					},
				};
			} else {
				const { institution, username, password, username2, username3 } =
					connectionData;
				const link = await this.belvoService.createLink(
					institution,
					username,
					password,
					username2,
					username3,
				);

				// Sync accounts immediately
				await this.belvoService.syncAccountsToDatabase(userId, link.id);

				return {
					id: link.id,
					provider: "belvo",
					linkId: link.id,
					institutionId: link.institution,
					userId,
					isActive: true,
					lastSyncAt: new Date(),
					metadata: {
						linkId: link.id,
						institution: link.institution,
					},
				};
			}
		} catch (error) {
			console.error(`Error completing ${provider} connection:`, error);
			throw new Error(`Failed to complete ${provider} connection`);
		}
	}

	/**
	 * Sync accounts for a specific user and provider
	 */
	async syncUserAccounts(
		userId: string,
		provider: BankingProvider,
		connectionId: string,
		accessToken?: string,
	): Promise<SyncResult> {
		const result: SyncResult = {
			accountsAdded: 0,
			accountsUpdated: 0,
			transactionsAdded: 0,
			errors: [],
		};

		try {
			if (provider === "plaid" && accessToken) {
				const accounts = await this.plaidService.syncAccountsToDatabase(
					userId,
					accessToken,
					connectionId,
				);
				result.accountsAdded = accounts.filter((a) => a.createdAt === a.updatedAt).length;
				result.accountsUpdated = accounts.filter((a) => a.createdAt !== a.updatedAt).length;
			} else if (provider === "belvo") {
				const accounts = await this.belvoService.syncAccountsToDatabase(
					userId,
					connectionId,
				);
				result.accountsAdded = accounts.filter((a) => a.createdAt === a.updatedAt).length;
				result.accountsUpdated = accounts.filter((a) => a.createdAt !== a.updatedAt).length;
			}
		} catch (error) {
			const errorMessage = `Failed to sync accounts for ${provider}: ${error}`;
			console.error(errorMessage);
			result.errors.push(errorMessage);
		}

		return result;
	}

	/**
	 * Sync transactions for a specific date range
	 */
	async syncUserTransactions(
		userId: string,
		provider: BankingProvider,
		connectionId: string,
		dateFrom: Date,
		dateTo: Date,
		accessToken?: string,
	): Promise<SyncResult> {
		const result: SyncResult = {
			accountsAdded: 0,
			accountsUpdated: 0,
			transactionsAdded: 0,
			errors: [],
		};

		try {
			if (provider === "plaid" && accessToken) {
				// Use transactions sync for ongoing sync
				const syncData = await this.plaidService.syncTransactions(accessToken);
				// Process and save transactions (implementation would go here)
				result.transactionsAdded = syncData.added.length;
			} else if (provider === "belvo") {
				const transactions = await this.belvoService.syncTransactionsToDatabase(
					userId,
					connectionId,
					dateFrom.toISOString().split("T")[0],
					dateTo.toISOString().split("T")[0],
				);
				result.transactionsAdded = transactions.length;
			}
		} catch (error) {
			const errorMessage = `Failed to sync transactions for ${provider}: ${error}`;
			console.error(errorMessage);
			result.errors.push(errorMessage);
		}

		return result;
	}

	/**
	 * Get all bank accounts for a user
	 */
	async getUserBankAccounts(userId: string) {
		try {
			const accounts = await db
				.select()
				.from(bankAccounts)
				.where(and(eq(bankAccounts.userId, userId), eq(bankAccounts.isActive, true)));

			return accounts;
		} catch (error) {
			console.error("Error fetching user bank accounts:", error);
			throw new Error("Failed to fetch bank accounts");
		}
	}

	/**
	 * Get transactions for a user within a date range
	 */
	async getUserTransactions(
		userId: string,
		dateFrom?: Date,
		dateTo?: Date,
		accountId?: string,
		limit = 100,
		offset = 0,
	) {
		try {
			let query = db
				.select()
				.from(transactions)
				.where(eq(transactions.userId, userId))
				.limit(limit)
				.offset(offset);

			// Add date filters if provided
			if (dateFrom) {
				query = query.where(gte(transactions.date, dateFrom));
			}
			if (dateTo) {
				query = query.where(lte(transactions.date, dateTo));
			}
			if (accountId) {
				query = query.where(eq(transactions.accountId, accountId));
			}

			const userTransactions = await query;
			return userTransactions;
		} catch (error) {
			console.error("Error fetching user transactions:", error);
			throw new Error("Failed to fetch transactions");
		}
	}

	/**
	 * Disconnect a bank connection
	 */
	async disconnectBank(
		userId: string,
		provider: BankingProvider,
		connectionId: string,
		accessToken?: string,
	) {
		try {
			// Remove from provider
			if (provider === "plaid" && accessToken) {
				await this.plaidService.removeItem(accessToken);
			} else if (provider === "belvo") {
				await this.belvoService.deleteLink(connectionId);
			}

			// Deactivate accounts in our database
			await db
				.update(bankAccounts)
				.set({ isActive: false, updatedAt: new Date() })
				.where(
					and(
						eq(bankAccounts.userId, userId),
						provider === "plaid"
							? eq(bankAccounts.plaidAccountId, connectionId)
							: eq(bankAccounts.belvoAccountId, connectionId),
					),
				);

			return true;
		} catch (error) {
			console.error(`Error disconnecting ${provider} bank:`, error);
			throw new Error(`Failed to disconnect ${provider} bank`);
		}
	}

	/**
	 * Get account balance summary for a user
	 */
	async getUserBalanceSummary(userId: string) {
		try {
			const accounts = await this.getUserBankAccounts(userId);

			const summary = {
				totalBalance: 0,
				totalAvailable: 0,
				accountsByType: {} as Record<string, { count: number; balance: number }>,
				lastSyncAt: null as Date | null,
			};

			for (const account of accounts) {
				const balance = parseFloat(account.balance || "0");
				const available = parseFloat(account.availableBalance || "0");

				summary.totalBalance += balance;
				summary.totalAvailable += available;

				if (!summary.accountsByType[account.accountType]) {
					summary.accountsByType[account.accountType] = { count: 0, balance: 0 };
				}

				summary.accountsByType[account.accountType].count++;
				summary.accountsByType[account.accountType].balance += balance;

				if (
					account.lastSyncAt &&
					(!summary.lastSyncAt || account.lastSyncAt > summary.lastSyncAt)
				) {
					summary.lastSyncAt = account.lastSyncAt;
				}
			}

			return summary;
		} catch (error) {
			console.error("Error getting balance summary:", error);
			throw new Error("Failed to get balance summary");
		}
	}

	/**
	 * Initialize scheduled sync for all active connections
	 */
	private initializeScheduledSync() {
		// Run every 4 hours
		cron.schedule("0 */4 * * *", async () => {
			console.log("Starting scheduled bank data sync...");
			await this.performScheduledSync();
		});

		// Run daily at 2 AM for transaction sync
		cron.schedule("0 2 * * *", async () => {
			console.log("Starting scheduled transaction sync...");
			await this.performScheduledTransactionSync();
		});
	}

	/**
	 * Perform scheduled sync for all active accounts
	 */
	private async performScheduledSync() {
		try {
			const activeAccounts = await db
				.select()
				.from(bankAccounts)
				.where(eq(bankAccounts.isActive, true));

			for (const account of activeAccounts) {
				try {
					if (account.plaidAccountId && account.metadata?.itemId) {
						// Sync Plaid account
						await this.syncUserAccounts(
							account.userId,
							"plaid",
							account.metadata.itemId,
							account.metadata.accessToken,
						);
					} else if (account.belvoAccountId && account.metadata?.linkId) {
						// Sync Belvo account
						await this.syncUserAccounts(
							account.userId,
							"belvo",
							account.metadata.linkId,
						);
					}
				} catch (error) {
					console.error(`Error syncing account ${account.id}:`, error);
				}
			}
		} catch (error) {
			console.error("Error in scheduled sync:", error);
		}
	}

	/**
	 * Perform scheduled transaction sync
	 */
	private async performScheduledTransactionSync() {
		try {
			const activeAccounts = await db
				.select()
				.from(bankAccounts)
				.where(eq(bankAccounts.isActive, true));

			const dateTo = new Date();
			const dateFrom = new Date();
			dateFrom.setDate(dateFrom.getDate() - 7); // Last 7 days

			for (const account of activeAccounts) {
				try {
					if (account.plaidAccountId && account.metadata?.itemId) {
						await this.syncUserTransactions(
							account.userId,
							"plaid",
							account.metadata.itemId,
							dateFrom,
							dateTo,
							account.metadata.accessToken,
						);
					} else if (account.belvoAccountId && account.metadata?.linkId) {
						await this.syncUserTransactions(
							account.userId,
							"belvo",
							account.metadata.linkId,
							dateFrom,
							dateTo,
						);
					}
				} catch (error) {
					console.error(`Error syncing transactions for account ${account.id}:`, error);
				}
			}
		} catch (error) {
			console.error("Error in scheduled transaction sync:", error);
		}
	}
}
