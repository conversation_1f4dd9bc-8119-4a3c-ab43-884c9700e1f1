import { and, desc, eq, gte, lte, sql } from "drizzle-orm";
import { db } from "../db";
import {
	aiInsights,
	bankAccounts,
	financialGoals,
	transactionCategories,
	transactions,
} from "../db/schema/financial";

export interface SpendingPattern {
	category: string;
	amount: number;
	frequency: number;
	trend: "increasing" | "decreasing" | "stable";
	percentage: number;
}

export interface FinancialInsight {
	type: "spending_pattern" | "anomaly" | "recommendation" | "forecast";
	title: string;
	description: string;
	confidence: number;
	data: any;
	actionable: boolean;
	priority: "low" | "medium" | "high";
}

export interface BudgetRecommendation {
	category: string;
	suggestedAmount: number;
	currentSpending: number;
	reasoning: string;
}

export class AIService {
	private apiKey: string;
	private apiUrl: string;

	constructor() {
		this.apiKey = process.env.AI_API_KEY || "";
		this.apiUrl = process.env.AI_API_URL || "";
	}

	/**
	 * Analyze spending patterns for a user
	 */
	async analyzeSpendingPatterns(
		userId: string,
		months: number = 3,
	): Promise<SpendingPattern[]> {
		try {
			const endDate = new Date();
			const startDate = new Date();
			startDate.setMonth(startDate.getMonth() - months);

			// Get user transactions with categories
			const userTransactions = await db
				.select({
					amount: transactions.amount,
					categoryId: transactions.categoryId,
					categoryName: transactionCategories.name,
					date: transactions.date,
					type: transactions.type,
				})
				.from(transactions)
				.leftJoin(
					transactionCategories,
					eq(transactions.categoryId, transactionCategories.id),
				)
				.where(
					and(
						eq(transactions.userId, userId),
						eq(transactions.type, "expense"),
						gte(transactions.date, startDate),
						lte(transactions.date, endDate),
					),
				)
				.orderBy(desc(transactions.date));

			// Group by category and analyze patterns
			const categorySpending = new Map<
				string,
				{ amounts: number[]; dates: Date[] }
			>();

			for (const transaction of userTransactions) {
				const category = transaction.categoryName || "Uncategorized";
				const amount = parseFloat(transaction.amount);

				if (!categorySpending.has(category)) {
					categorySpending.set(category, { amounts: [], dates: [] });
				}

				categorySpending.get(category)!.amounts.push(amount);
				categorySpending.get(category)!.dates.push(transaction.date);
			}

			// Calculate patterns
			const patterns: SpendingPattern[] = [];
			const totalSpending = userTransactions.reduce(
				(sum, t) => sum + parseFloat(t.amount),
				0,
			);

			for (const [category, data] of categorySpending) {
				const totalAmount = data.amounts.reduce(
					(sum, amount) => sum + amount,
					0,
				);
				const frequency = data.amounts.length;
				const percentage = (totalAmount / totalSpending) * 100;

				// Calculate trend (simplified)
				const trend = this.calculateTrend(data.amounts, data.dates);

				patterns.push({
					category,
					amount: totalAmount,
					frequency,
					trend,
					percentage,
				});
			}

			// Sort by amount descending
			return patterns.sort((a, b) => b.amount - a.amount);
		} catch (error) {
			console.error("Error analyzing spending patterns:", error);
			throw new Error("Failed to analyze spending patterns");
		}
	}

	/**
	 * Detect spending anomalies
	 */
	async detectAnomalies(userId: string): Promise<FinancialInsight[]> {
		try {
			const insights: FinancialInsight[] = [];
			const endDate = new Date();
			const startDate = new Date();
			startDate.setDate(startDate.getDate() - 30); // Last 30 days

			// Get recent transactions
			const recentTransactions = await db
				.select()
				.from(transactions)
				.where(
					and(
						eq(transactions.userId, userId),
						gte(transactions.date, startDate),
						lte(transactions.date, endDate),
					),
				);

			// Get historical average for comparison
			const historicalStartDate = new Date();
			historicalStartDate.setMonth(historicalStartDate.getMonth() - 6);

			const historicalTransactions = await db
				.select()
				.from(transactions)
				.where(
					and(
						eq(transactions.userId, userId),
						gte(transactions.date, historicalStartDate),
						lte(transactions.date, startDate),
					),
				);

			// Detect large transactions
			const largeTransactions = recentTransactions.filter(
				(t) => parseFloat(t.amount) > 500, // Configurable threshold
			);

			for (const transaction of largeTransactions) {
				insights.push({
					type: "anomaly",
					title: "Large Transaction Detected",
					description: `Unusual transaction of ${transaction.amount} ${transaction.currency} for ${transaction.description}`,
					confidence: 0.8,
					data: { transaction },
					actionable: true,
					priority: "medium",
				});
			}

			// Detect spending spikes
			const recentSpending = recentTransactions
				.filter((t) => t.type === "expense")
				.reduce((sum, t) => sum + parseFloat(t.amount), 0);

			const historicalMonthlyAverage =
				historicalTransactions
					.filter((t) => t.type === "expense")
					.reduce((sum, t) => sum + parseFloat(t.amount), 0) / 6;

			if (recentSpending > historicalMonthlyAverage * 1.5) {
				insights.push({
					type: "anomaly",
					title: "Spending Spike Detected",
					description: `Your spending this month (${recentSpending.toFixed(2)}) is significantly higher than your average (${historicalMonthlyAverage.toFixed(2)})`,
					confidence: 0.9,
					data: { recentSpending, historicalAverage: historicalMonthlyAverage },
					actionable: true,
					priority: "high",
				});
			}

			return insights;
		} catch (error) {
			console.error("Error detecting anomalies:", error);
			throw new Error("Failed to detect anomalies");
		}
	}

	/**
	 * Generate personalized recommendations
	 */
	async generateRecommendations(userId: string): Promise<FinancialInsight[]> {
		try {
			const insights: FinancialInsight[] = [];

			// Analyze spending patterns
			const patterns = await this.analyzeSpendingPatterns(userId);

			// Get user's financial goals
			const goals = await db
				.select()
				.from(financialGoals)
				.where(
					and(
						eq(financialGoals.userId, userId),
						eq(financialGoals.status, "active"),
					),
				);

			// Generate budget recommendations
			const budgetRecommendations =
				await this.generateBudgetRecommendations(patterns);

			for (const recommendation of budgetRecommendations) {
				insights.push({
					type: "recommendation",
					title: `Budget Suggestion for ${recommendation.category}`,
					description: recommendation.reasoning,
					confidence: 0.7,
					data: recommendation,
					actionable: true,
					priority: "medium",
				});
			}

			// Savings recommendations
			const totalMonthlySpending = patterns.reduce(
				(sum, p) => sum + p.amount,
				0,
			);
			const suggestedSavings = totalMonthlySpending * 0.2; // 20% savings rule

			insights.push({
				type: "recommendation",
				title: "Savings Opportunity",
				description: `Based on your spending patterns, you could potentially save $${suggestedSavings.toFixed(2)} per month by optimizing your largest expense categories.`,
				confidence: 0.6,
				data: { suggestedSavings, currentSpending: totalMonthlySpending },
				actionable: true,
				priority: "medium",
			});

			// Goal-based recommendations
			for (const goal of goals) {
				const monthsToGoal = goal.targetDate
					? Math.ceil(
							(goal.targetDate.getTime() - Date.now()) /
								(1000 * 60 * 60 * 24 * 30),
						)
					: 12;

				const currentAmount = parseFloat(goal.currentAmount || "0");
				const targetAmount = parseFloat(goal.targetAmount || "0");
				const remainingAmount = targetAmount - currentAmount;
				const monthlyRequired = remainingAmount / monthsToGoal;

				if (monthlyRequired > 0) {
					insights.push({
						type: "recommendation",
						title: `${goal.name} Progress`,
						description: `To reach your goal of $${targetAmount}, you need to save $${monthlyRequired.toFixed(2)} per month for the next ${monthsToGoal} months.`,
						confidence: 0.9,
						data: { goal, monthlyRequired, monthsToGoal },
						actionable: true,
						priority: "high",
					});
				}
			}

			return insights;
		} catch (error) {
			console.error("Error generating recommendations:", error);
			throw new Error("Failed to generate recommendations");
		}
	}

	/**
	 * Generate spending forecast
	 */
	async generateSpendingForecast(
		userId: string,
		months: number = 3,
	): Promise<FinancialInsight> {
		try {
			const patterns = await this.analyzeSpendingPatterns(userId, 6); // Use 6 months of data

			// Simple linear regression for forecasting
			const totalMonthlySpending = patterns.reduce(
				(sum, p) => sum + p.amount,
				0,
			);

			// Calculate trend multiplier
			const trendMultiplier =
				patterns.reduce((avg, p) => {
					switch (p.trend) {
						case "increasing":
							return avg + 0.05;
						case "decreasing":
							return avg - 0.05;
						default:
							return avg;
					}
				}, 1) / patterns.length;

			const forecastData = [];
			for (let i = 1; i <= months; i++) {
				const forecastAmount =
					totalMonthlySpending * Math.pow(trendMultiplier, i);
				forecastData.push({
					month: i,
					amount: forecastAmount,
					categories: patterns.map((p) => ({
						category: p.category,
						amount: p.amount * Math.pow(trendMultiplier, i),
					})),
				});
			}

			return {
				type: "forecast",
				title: `${months}-Month Spending Forecast`,
				description: `Based on your spending patterns, we predict your expenses for the next ${months} months.`,
				confidence: 0.7,
				data: { forecast: forecastData, totalMonthlySpending },
				actionable: false,
				priority: "low",
			};
		} catch (error) {
			console.error("Error generating forecast:", error);
			throw new Error("Failed to generate forecast");
		}
	}

	/**
	 * Save insights to database
	 */
	async saveInsights(
		userId: string,
		insights: FinancialInsight[],
	): Promise<void> {
		try {
			for (const insight of insights) {
				await db.insert(aiInsights).values({
					userId,
					type: insight.type,
					title: insight.title,
					description: insight.description,
					confidence: insight.confidence.toString(),
					data: insight.data,
					validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
				});
			}
		} catch (error) {
			console.error("Error saving insights:", error);
			throw new Error("Failed to save insights");
		}
	}

	/**
	 * Get user insights from database
	 */
	async getUserInsights(userId: string, limit: number = 10): Promise<any[]> {
		try {
			const insights = await db
				.select()
				.from(aiInsights)
				.where(
					and(
						eq(aiInsights.userId, userId),
						eq(aiInsights.isArchived, false),
						gte(aiInsights.validUntil, new Date()),
					),
				)
				.orderBy(desc(aiInsights.createdAt))
				.limit(limit);

			return insights;
		} catch (error) {
			console.error("Error fetching user insights:", error);
			throw new Error("Failed to fetch insights");
		}
	}

	/**
	 * Generate comprehensive financial analysis
	 */
	async generateComprehensiveAnalysis(userId: string): Promise<{
		patterns: SpendingPattern[];
		anomalies: FinancialInsight[];
		recommendations: FinancialInsight[];
		forecast: FinancialInsight;
	}> {
		try {
			const [patterns, anomalies, recommendations, forecast] =
				await Promise.all([
					this.analyzeSpendingPatterns(userId),
					this.detectAnomalies(userId),
					this.generateRecommendations(userId),
					this.generateSpendingForecast(userId),
				]);

			// Save all insights to database
			const allInsights = [...anomalies, ...recommendations, forecast];
			await this.saveInsights(userId, allInsights);

			return {
				patterns,
				anomalies,
				recommendations,
				forecast,
			};
		} catch (error) {
			console.error("Error generating comprehensive analysis:", error);
			throw new Error("Failed to generate comprehensive analysis");
		}
	}

	/**
	 * Categorize transaction using AI (placeholder for actual AI integration)
	 */
	async categorizeTransaction(
		description: string,
		amount: number,
		merchantName?: string,
	): Promise<string> {
		try {
			// This would integrate with an actual AI service
			// For now, use simple keyword matching
			const keywords = {
				"Food & Dining": [
					"restaurant",
					"cafe",
					"food",
					"dining",
					"pizza",
					"burger",
					"starbucks",
					"mcdonald",
				],
				Transportation: [
					"gas",
					"fuel",
					"uber",
					"lyft",
					"taxi",
					"parking",
					"metro",
					"bus",
				],
				Shopping: ["amazon", "target", "walmart", "store", "shop", "retail"],
				Entertainment: [
					"movie",
					"theater",
					"netflix",
					"spotify",
					"game",
					"concert",
				],
				"Bills & Utilities": [
					"electric",
					"water",
					"internet",
					"phone",
					"utility",
					"bill",
				],
				Healthcare: ["doctor", "hospital", "pharmacy", "medical", "health"],
			};

			const searchText = `${description} ${merchantName || ""}`.toLowerCase();

			for (const [category, categoryKeywords] of Object.entries(keywords)) {
				if (categoryKeywords.some((keyword) => searchText.includes(keyword))) {
					return category;
				}
			}

			return "Other";
		} catch (error) {
			console.error("Error categorizing transaction:", error);
			return "Other";
		}
	}

	/**
	 * Calculate trend from historical data
	 */
	private calculateTrend(
		amounts: number[],
		dates: Date[],
	): "increasing" | "decreasing" | "stable" {
		if (amounts.length < 2) return "stable";

		// Simple trend calculation based on first and last values
		const firstHalf = amounts.slice(0, Math.floor(amounts.length / 2));
		const secondHalf = amounts.slice(Math.floor(amounts.length / 2));

		const firstAvg =
			firstHalf.reduce((sum, a) => sum + a, 0) / firstHalf.length;
		const secondAvg =
			secondHalf.reduce((sum, a) => sum + a, 0) / secondHalf.length;

		const changePercent = ((secondAvg - firstAvg) / firstAvg) * 100;

		if (changePercent > 10) return "increasing";
		if (changePercent < -10) return "decreasing";
		return "stable";
	}

	/**
	 * Generate budget recommendations based on spending patterns
	 */
	private async generateBudgetRecommendations(
		patterns: SpendingPattern[],
	): Promise<BudgetRecommendation[]> {
		const recommendations: BudgetRecommendation[] = [];

		for (const pattern of patterns.slice(0, 5)) {
			// Top 5 categories
			let suggestedAmount = pattern.amount;
			let reasoning = "";

			if (pattern.trend === "increasing") {
				suggestedAmount = pattern.amount * 0.9; // Reduce by 10%
				reasoning = `Your ${pattern.category} spending is increasing. Consider reducing by 10% to control costs.`;
			} else if (pattern.trend === "stable" && pattern.percentage > 30) {
				suggestedAmount = pattern.amount * 0.95; // Reduce by 5%
				reasoning = `${pattern.category} represents ${pattern.percentage.toFixed(1)}% of your spending. A small reduction could yield significant savings.`;
			} else {
				suggestedAmount = pattern.amount * 1.05; // Allow 5% increase
				reasoning = `Your ${pattern.category} spending is well-controlled. You can maintain or slightly increase this budget.`;
			}

			recommendations.push({
				category: pattern.category,
				suggestedAmount,
				currentSpending: pattern.amount,
				reasoning,
			});
		}

		return recommendations;
	}
}
