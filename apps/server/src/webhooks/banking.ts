import { eq } from "drizzle-orm";
import type { FastifyReply, FastifyRequest } from "fastify";
import { db } from "../db";
import { bankAccounts } from "../db/schema/financial";
import { BankingService } from "../services/banking";

const bankingService = new BankingService();

export interface PlaidWebhookPayload {
	webhook_type: string;
	webhook_code: string;
	item_id: string;
	error?: {
		error_type: string;
		error_code: string;
		error_message: string;
	};
	new_transactions?: number;
	removed_transactions?: string[];
	modified_transactions?: string[];
}

export interface BelvoWebhookPayload {
	webhook_type: string;
	webhook_code: string;
	link_id: string;
	request_id: string;
	external_id?: string;
	data?: Record<string, unknown>;
}

/**
 * Handle Plaid webhooks
 */
export async function handlePlaidWebhook(
	request: FastifyRequest,
	reply: FastifyReply,
) {
	try {
		const payload = request.body as PlaidWebhookPayload;
		console.log("Received Plaid webhook:", payload);

		switch (payload.webhook_type) {
			case "TRANSACTIONS":
				await handlePlaidTransactionsWebhook(payload);
				break;
			case "ITEM":
				await handlePlaidItemWebhook(payload);
				break;
			case "AUTH":
				await handlePlaidAuthWebhook(payload);
				break;
			case "ACCOUNTS":
				await handlePlaidAccountsWebhook(payload);
				break;
			default:
				console.log(`Unhandled Plaid webhook type: ${payload.webhook_type}`);
		}

		reply.status(200).send({ status: "success" });
	} catch (error) {
		console.error("Error handling Plaid webhook:", error);
		reply.status(500).send({ error: "Internal server error" });
	}
}

/**
 * Handle Belvo webhooks
 */
export async function handleBelvoWebhook(
	request: FastifyRequest,
	reply: FastifyReply,
) {
	try {
		const payload = request.body as BelvoWebhookPayload;
		console.log("Received Belvo webhook:", payload);

		switch (payload.webhook_type) {
			case "TRANSACTIONS":
				await handleBelvoTransactionsWebhook(payload);
				break;
			case "ACCOUNTS":
				await handleBelvoAccountsWebhook(payload);
				break;
			case "LINKS":
				await handleBelvoLinksWebhook(payload);
				break;
			default:
				console.log(`Unhandled Belvo webhook type: ${payload.webhook_type}`);
		}

		reply.status(200).send({ status: "success" });
	} catch (error) {
		console.error("Error handling Belvo webhook:", error);
		reply.status(500).send({ error: "Internal server error" });
	}
}

/**
 * Handle Plaid transactions webhooks
 */
async function handlePlaidTransactionsWebhook(payload: PlaidWebhookPayload) {
	const { webhook_code, item_id } = payload;

	// Find accounts associated with this item
	const accounts = await db
		.select()
		.from(bankAccounts)
		.where(eq(bankAccounts.metadata, { itemId: item_id }));

	if (accounts.length === 0) {
		console.warn(`No accounts found for Plaid item: ${item_id}`);
		return;
	}

	const userId = accounts[0].userId;

	switch (webhook_code) {
		case "SYNC_UPDATES_AVAILABLE":
			console.log(`Sync updates available for item: ${item_id}`);
			// Trigger sync for this user's Plaid accounts
			await bankingService.syncUserAccounts(userId, "plaid", item_id);
			break;

		case "DEFAULT_UPDATE":
		case "INITIAL_UPDATE": {
			console.log(`Transaction updates for item: ${item_id}`);
			// Sync recent transactions
			const dateTo = new Date();
			const dateFrom = new Date();
			dateFrom.setDate(dateFrom.getDate() - 30); // Last 30 days

			await bankingService.syncUserTransactions(
				userId,
				"plaid",
				item_id,
				dateFrom,
				dateTo,
			);
			break;
		}
		case "HISTORICAL_UPDATE": {
			console.log(`Historical transaction update for item: ${item_id}`);
			// Sync historical transactions (larger date range)
			const historicalDateTo = new Date();
			const historicalDateFrom = new Date();
			historicalDateFrom.setFullYear(historicalDateFrom.getFullYear() - 2); // Last 2 years

			await bankingService.syncUserTransactions(
				userId,
				"plaid",
				item_id,
				historicalDateFrom,
				historicalDateTo,
			);
			break;
		}
		default:
			console.log(`Unhandled Plaid transactions webhook code: ${webhook_code}`);
	}
}

/**
 * Handle Plaid item webhooks
 */
async function handlePlaidItemWebhook(payload: PlaidWebhookPayload) {
	const { webhook_code, item_id, error } = payload;

	switch (webhook_code) {
		case "ERROR":
			console.error(`Plaid item error for ${item_id}:`, error);
			// Mark accounts as needing attention
			await db
				.update(bankAccounts)
				.set({
					metadata: { error: error, needsAttention: true },
					updatedAt: new Date(),
				})
				.where(eq(bankAccounts.metadata, { itemId: item_id }));
			break;

		case "PENDING_EXPIRATION":
			console.warn(`Plaid item pending expiration: ${item_id}`);
			// Notify user that they need to re-authenticate
			break;

		case "USER_PERMISSION_REVOKED":
			console.log(`User revoked permissions for item: ${item_id}`);
			// Deactivate accounts
			await db
				.update(bankAccounts)
				.set({ isActive: false, updatedAt: new Date() })
				.where(eq(bankAccounts.metadata, { itemId: item_id }));
			break;

		case "WEBHOOK_UPDATE_ACKNOWLEDGED":
			console.log(`Webhook update acknowledged for item: ${item_id}`);
			break;

		default:
			console.log(`Unhandled Plaid item webhook code: ${webhook_code}`);
	}
}

/**
 * Handle Plaid auth webhooks
 */
async function handlePlaidAuthWebhook(payload: PlaidWebhookPayload) {
	const { webhook_code, item_id } = payload;

	switch (webhook_code) {
		case "AUTOMATICALLY_VERIFIED":
			console.log(`Auth automatically verified for item: ${item_id}`);
			break;

		case "VERIFICATION_EXPIRED":
			console.warn(`Auth verification expired for item: ${item_id}`);
			break;

		default:
			console.log(`Unhandled Plaid auth webhook code: ${webhook_code}`);
	}
}

/**
 * Handle Plaid accounts webhooks
 */
async function handlePlaidAccountsWebhook(payload: PlaidWebhookPayload) {
	const { webhook_code, item_id } = payload;

	switch (webhook_code) {
		case "DEFAULT_UPDATE": {
			console.log(`Account updates available for item: ${item_id}`);
			// Find user and sync accounts
			const accounts = await db
				.select()
				.from(bankAccounts)
				.where(eq(bankAccounts.metadata, { itemId: item_id }));

			if (accounts.length > 0) {
				const userId = accounts[0].userId;
				await bankingService.syncUserAccounts(userId, "plaid", item_id);
			}
			break;
		}
		default:
			console.log(`Unhandled Plaid accounts webhook code: ${webhook_code}`);
	}
}

/**
 * Handle Belvo transactions webhooks
 */
async function handleBelvoTransactionsWebhook(payload: BelvoWebhookPayload) {
	const { webhook_code, link_id } = payload;

	// Find accounts associated with this link
	const accounts = await db
		.select()
		.from(bankAccounts)
		.where(eq(bankAccounts.metadata, { linkId: link_id }));

	if (accounts.length === 0) {
		console.warn(`No accounts found for Belvo link: ${link_id}`);
		return;
	}

	const userId = accounts[0].userId;

	switch (webhook_code) {
		case "TRANSACTIONS_UPDATE": {
			console.log(`Transaction updates for link: ${link_id}`);
			// Sync recent transactions
			const dateTo = new Date();
			const dateFrom = new Date();
			dateFrom.setDate(dateFrom.getDate() - 30); // Last 30 days

			await bankingService.syncUserTransactions(
				userId,
				"belvo",
				link_id,
				dateFrom,
				dateTo,
			);
			break;
		}
		default:
			console.log(`Unhandled Belvo transactions webhook code: ${webhook_code}`);
	}
}

/**
 * Handle Belvo accounts webhooks
 */
async function handleBelvoAccountsWebhook(payload: BelvoWebhookPayload) {
	const { webhook_code, link_id } = payload;

	switch (webhook_code) {
		case "ACCOUNTS_UPDATE": {
			console.log(`Account updates for link: ${link_id}`);
			// Find user and sync accounts
			const accounts = await db
				.select()
				.from(bankAccounts)
				.where(eq(bankAccounts.metadata, { linkId: link_id }));

			if (accounts.length > 0) {
				const userId = accounts[0].userId;
				await bankingService.syncUserAccounts(userId, "belvo", link_id);
			}
			break;
		}

		default:
			console.log(`Unhandled Belvo accounts webhook code: ${webhook_code}`);
	}
}

/**
 * Handle Belvo links webhooks
 */
async function handleBelvoLinksWebhook(payload: BelvoWebhookPayload) {
	const { webhook_code, link_id } = payload;

	switch (webhook_code) {
		case "LINK_CREATED":
			console.log(`Link created: ${link_id}`);
			break;

		case "LINK_UPDATED":
			console.log(`Link updated: ${link_id}`);
			break;

		case "LINK_DELETED":
			console.log(`Link deleted: ${link_id}`);
			// Deactivate accounts
			await db
				.update(bankAccounts)
				.set({ isActive: false, updatedAt: new Date() })
				.where(eq(bankAccounts.metadata, { linkId: link_id }));
			break;

		default:
			console.log(`Unhandled Belvo links webhook code: ${webhook_code}`);
	}
}
