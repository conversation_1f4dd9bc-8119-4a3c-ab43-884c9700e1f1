{"id": "3acc77cf-0e6e-4629-a0ae-5fb3138283e4", "prevId": "********-0000-0000-0000-************", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.two_factor": {"name": "two_factor", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "secret": {"name": "secret", "type": "text", "primaryKey": false, "notNull": true}, "backup_codes": {"name": "backup_codes", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"two_factor_user_id_user_id_fk": {"name": "two_factor_user_id_user_id_fk", "tableFrom": "two_factor", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "date", "primaryKey": false, "notNull": false}, "preferred_language": {"name": "preferred_language", "type": "text", "primaryKey": false, "notNull": false, "default": "'en'"}, "timezone": {"name": "timezone", "type": "text", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "notification_preferences": {"name": "notification_preferences", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\"email\":true,\"push\":true,\"sms\":false,\"marketing\":false}'::jsonb"}, "two_factor_enabled": {"name": "two_factor_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ai_insights": {"name": "ai_insights", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "confidence": {"name": "confidence", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_archived": {"name": "is_archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "valid_until": {"name": "valid_until", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ai_insights_user_id_user_id_fk": {"name": "ai_insights_user_id_user_id_fk", "tableFrom": "ai_insights", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bank_accounts": {"name": "bank_accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "institution_id": {"name": "institution_id", "type": "uuid", "primaryKey": false, "notNull": false}, "account_type": {"name": "account_type", "type": "account_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "account_name": {"name": "account_name", "type": "text", "primaryKey": false, "notNull": true}, "account_number": {"name": "account_number", "type": "text", "primaryKey": false, "notNull": false}, "routing_number": {"name": "routing_number", "type": "text", "primaryKey": false, "notNull": false}, "balance": {"name": "balance", "type": "numeric(15, 2)", "primaryKey": false, "notNull": false, "default": "'0.00'"}, "available_balance": {"name": "available_balance", "type": "numeric(15, 2)", "primaryKey": false, "notNull": false, "default": "'0.00'"}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'USD'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "last_sync_at": {"name": "last_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "plaid_account_id": {"name": "plaid_account_id", "type": "text", "primaryKey": false, "notNull": false}, "belvo_account_id": {"name": "belvo_account_id", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"bank_accounts_user_id_user_id_fk": {"name": "bank_accounts_user_id_user_id_fk", "tableFrom": "bank_accounts", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "bank_accounts_institution_id_financial_institutions_id_fk": {"name": "bank_accounts_institution_id_financial_institutions_id_fk", "tableFrom": "bank_accounts", "tableTo": "financial_institutions", "columnsFrom": ["institution_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.budget_categories": {"name": "budget_categories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "budget_id": {"name": "budget_id", "type": "uuid", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": true}, "allocated_amount": {"name": "allocated_amount", "type": "numeric(15, 2)", "primaryKey": false, "notNull": true}, "spent_amount": {"name": "spent_amount", "type": "numeric(15, 2)", "primaryKey": false, "notNull": false, "default": "'0.00'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"budget_categories_budget_id_budgets_id_fk": {"name": "budget_categories_budget_id_budgets_id_fk", "tableFrom": "budget_categories", "tableTo": "budgets", "columnsFrom": ["budget_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "budget_categories_category_id_transaction_categories_id_fk": {"name": "budget_categories_category_id_transaction_categories_id_fk", "tableFrom": "budget_categories", "tableTo": "transaction_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.budgets": {"name": "budgets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "period": {"name": "period", "type": "budget_period", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'monthly'"}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "total_budget": {"name": "total_budget", "type": "numeric(15, 2)", "primaryKey": false, "notNull": true}, "total_spent": {"name": "total_spent", "type": "numeric(15, 2)", "primaryKey": false, "notNull": false, "default": "'0.00'"}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'USD'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "alert_threshold": {"name": "alert_threshold", "type": "integer", "primaryKey": false, "notNull": false, "default": 80}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"budgets_user_id_user_id_fk": {"name": "budgets_user_id_user_id_fk", "tableFrom": "budgets", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.financial_goals": {"name": "financial_goals", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "goal_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "goal_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'active'"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "target_amount": {"name": "target_amount", "type": "numeric(15, 2)", "primaryKey": false, "notNull": false}, "current_amount": {"name": "current_amount", "type": "numeric(15, 2)", "primaryKey": false, "notNull": false, "default": "'0.00'"}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'USD'"}, "target_date": {"name": "target_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_date": {"name": "completed_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "linked_account_id": {"name": "linked_account_id", "type": "uuid", "primaryKey": false, "notNull": false}, "auto_contribute": {"name": "auto_contribute", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "contribution_amount": {"name": "contribution_amount", "type": "numeric(15, 2)", "primaryKey": false, "notNull": false}, "contribution_frequency": {"name": "contribution_frequency", "type": "text", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"financial_goals_user_id_user_id_fk": {"name": "financial_goals_user_id_user_id_fk", "tableFrom": "financial_goals", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "financial_goals_linked_account_id_bank_accounts_id_fk": {"name": "financial_goals_linked_account_id_bank_accounts_id_fk", "tableFrom": "financial_goals", "tableTo": "bank_accounts", "columnsFrom": ["linked_account_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.financial_institutions": {"name": "financial_institutions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "plaid_institution_id": {"name": "plaid_institution_id", "type": "text", "primaryKey": false, "notNull": false}, "belvo_institution_id": {"name": "belvo_institution_id", "type": "text", "primaryKey": false, "notNull": false}, "supported_countries": {"name": "supported_countries", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "notification_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "action_url": {"name": "action_url", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "scheduled_for": {"name": "scheduled_for", "type": "timestamp", "primaryKey": false, "notNull": false}, "sent_at": {"name": "sent_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"notifications_user_id_user_id_fk": {"name": "notifications_user_id_user_id_fk", "tableFrom": "notifications", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.recurring_transactions": {"name": "recurring_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "numeric(15, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'USD'"}, "frequency": {"name": "frequency", "type": "text", "primaryKey": false, "notNull": true}, "next_due_date": {"name": "next_due_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "last_processed_date": {"name": "last_processed_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "reminder_days": {"name": "reminder_days", "type": "integer", "primaryKey": false, "notNull": false, "default": 3}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"recurring_transactions_user_id_user_id_fk": {"name": "recurring_transactions_user_id_user_id_fk", "tableFrom": "recurring_transactions", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "recurring_transactions_account_id_bank_accounts_id_fk": {"name": "recurring_transactions_account_id_bank_accounts_id_fk", "tableFrom": "recurring_transactions", "tableTo": "bank_accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "recurring_transactions_category_id_transaction_categories_id_fk": {"name": "recurring_transactions_category_id_transaction_categories_id_fk", "tableFrom": "recurring_transactions", "tableTo": "transaction_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.transaction_categories": {"name": "transaction_categories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"transaction_categories_parent_id_transaction_categories_id_fk": {"name": "transaction_categories_parent_id_transaction_categories_id_fk", "tableFrom": "transaction_categories", "tableTo": "transaction_categories", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.transactions": {"name": "transactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "transaction_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "transaction_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'posted'"}, "amount": {"name": "amount", "type": "numeric(15, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'USD'"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "merchant_name": {"name": "merchant_name", "type": "text", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp", "primaryKey": false, "notNull": true}, "authorized_date": {"name": "authorized_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "jsonb", "primaryKey": false, "notNull": false}, "plaid_transaction_id": {"name": "plaid_transaction_id", "type": "text", "primaryKey": false, "notNull": false}, "belvo_transaction_id": {"name": "belvo_transaction_id", "type": "text", "primaryKey": false, "notNull": false}, "is_recurring": {"name": "is_recurring", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "recurring_group_id": {"name": "recurring_group_id", "type": "uuid", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"transactions_user_id_user_id_fk": {"name": "transactions_user_id_user_id_fk", "tableFrom": "transactions", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "transactions_account_id_bank_accounts_id_fk": {"name": "transactions_account_id_bank_accounts_id_fk", "tableFrom": "transactions", "tableTo": "bank_accounts", "columnsFrom": ["account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "transactions_category_id_transaction_categories_id_fk": {"name": "transactions_category_id_transaction_categories_id_fk", "tableFrom": "transactions", "tableTo": "transaction_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_preferences": {"name": "user_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "default_currency": {"name": "default_currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "date_format": {"name": "date_format", "type": "text", "primaryKey": false, "notNull": false, "default": "'MM/DD/YYYY'"}, "number_format": {"name": "number_format", "type": "text", "primaryKey": false, "notNull": false, "default": "'en-US'"}, "budget_start_day": {"name": "budget_start_day", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "week_start_day": {"name": "week_start_day", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "enable_ai_insights": {"name": "enable_ai_insights", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "enable_spending_alerts": {"name": "enable_spending_alerts", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "enable_bill_reminders": {"name": "enable_bill_reminders", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "data_retention_months": {"name": "data_retention_months", "type": "integer", "primaryKey": false, "notNull": false, "default": 24}, "privacy_settings": {"name": "privacy_settings", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\"shareAnonymousData\":false,\"enableAnalytics\":true}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_preferences_user_id_user_id_fk": {"name": "user_preferences_user_id_user_id_fk", "tableFrom": "user_preferences", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_preferences_user_id_unique": {"name": "user_preferences_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.billing_events": {"name": "billing_events", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": true}, "stripe_event_id": {"name": "stripe_event_id", "type": "text", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}, "processed": {"name": "processed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "retry_count": {"name": "retry_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"billing_events_user_id_user_id_fk": {"name": "billing_events_user_id_user_id_fk", "tableFrom": "billing_events", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.feature_flags": {"name": "feature_flags", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_enabled": {"name": "is_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "rollout_percentage": {"name": "rollout_percentage", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "target_tiers": {"name": "target_tiers", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "target_users": {"name": "target_users", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "conditions": {"name": "conditions", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"feature_flags_name_unique": {"name": "feature_flags_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payments": {"name": "payments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'USD'"}, "status": {"name": "status", "type": "payment_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "stripe_payment_intent_id": {"name": "stripe_payment_intent_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_charge_id": {"name": "stripe_charge_id", "type": "text", "primaryKey": false, "notNull": false}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": false}, "failure_reason": {"name": "failure_reason", "type": "text", "primaryKey": false, "notNull": false}, "refunded_amount": {"name": "refunded_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "refunded_at": {"name": "refunded_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"payments_user_id_user_id_fk": {"name": "payments_user_id_user_id_fk", "tableFrom": "payments", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payments_subscription_id_user_subscriptions_id_fk": {"name": "payments_subscription_id_user_subscriptions_id_fk", "tableFrom": "payments", "tableTo": "user_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.promo_code_usage": {"name": "promo_code_usage", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "promo_code_id": {"name": "promo_code_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "discount_amount": {"name": "discount_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "used_at": {"name": "used_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"promo_code_usage_promo_code_id_promo_codes_id_fk": {"name": "promo_code_usage_promo_code_id_promo_codes_id_fk", "tableFrom": "promo_code_usage", "tableTo": "promo_codes", "columnsFrom": ["promo_code_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "promo_code_usage_user_id_user_id_fk": {"name": "promo_code_usage_user_id_user_id_fk", "tableFrom": "promo_code_usage", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "promo_code_usage_subscription_id_user_subscriptions_id_fk": {"name": "promo_code_usage_subscription_id_user_subscriptions_id_fk", "tableFrom": "promo_code_usage", "tableTo": "user_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.promo_codes": {"name": "promo_codes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "discount_type": {"name": "discount_type", "type": "text", "primaryKey": false, "notNull": true}, "discount_value": {"name": "discount_value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "max_uses": {"name": "max_uses", "type": "integer", "primaryKey": false, "notNull": false}, "used_count": {"name": "used_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "valid_from": {"name": "valid_from", "type": "timestamp", "primaryKey": false, "notNull": true}, "valid_until": {"name": "valid_until", "type": "timestamp", "primaryKey": false, "notNull": false}, "applicable_plans": {"name": "applicable_plans", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "stripe_promotion_code_id": {"name": "stripe_promotion_code_id", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"promo_codes_code_unique": {"name": "promo_codes_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription_plans": {"name": "subscription_plans", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "tier": {"name": "tier", "type": "subscription_tier", "typeSchema": "public", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'USD'"}, "billing_interval": {"name": "billing_interval", "type": "text", "primaryKey": false, "notNull": true}, "trial_days": {"name": "trial_days", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "features": {"name": "features", "type": "jsonb", "primaryKey": false, "notNull": true}, "limits": {"name": "limits", "type": "jsonb", "primaryKey": false, "notNull": true}, "stripe_product_id": {"name": "stripe_product_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_price_id": {"name": "stripe_price_id", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.usage_tracking": {"name": "usage_tracking", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "usage_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "period": {"name": "period", "type": "text", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"usage_tracking_user_id_user_id_fk": {"name": "usage_tracking_user_id_user_id_fk", "tableFrom": "usage_tracking", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_feature_access": {"name": "user_feature_access", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "feature_id": {"name": "feature_id", "type": "uuid", "primaryKey": false, "notNull": true}, "has_access": {"name": "has_access", "type": "boolean", "primaryKey": false, "notNull": true}, "granted_at": {"name": "granted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "revokedAt": {"name": "revokedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_feature_access_user_id_user_id_fk": {"name": "user_feature_access_user_id_user_id_fk", "tableFrom": "user_feature_access", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_feature_access_feature_id_feature_flags_id_fk": {"name": "user_feature_access_feature_id_feature_flags_id_fk", "tableFrom": "user_feature_access", "tableTo": "feature_flags", "columnsFrom": ["feature_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_subscriptions": {"name": "user_subscriptions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "subscription_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "text", "primaryKey": false, "notNull": false}, "current_period_start": {"name": "current_period_start", "type": "timestamp", "primaryKey": false, "notNull": true}, "current_period_end": {"name": "current_period_end", "type": "timestamp", "primaryKey": false, "notNull": true}, "trial_start": {"name": "trial_start", "type": "timestamp", "primaryKey": false, "notNull": false}, "trial_end": {"name": "trial_end", "type": "timestamp", "primaryKey": false, "notNull": false}, "cancelled_at": {"name": "cancelled_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "cancel_at_period_end": {"name": "cancel_at_period_end", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_subscriptions_user_id_user_id_fk": {"name": "user_subscriptions_user_id_user_id_fk", "tableFrom": "user_subscriptions", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_subscriptions_plan_id_subscription_plans_id_fk": {"name": "user_subscriptions_plan_id_subscription_plans_id_fk", "tableFrom": "user_subscriptions", "tableTo": "subscription_plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_subscriptions_user_id_unique": {"name": "user_subscriptions_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.account_type": {"name": "account_type", "schema": "public", "values": ["checking", "savings", "credit_card", "investment", "loan", "mortgage", "other"]}, "public.budget_period": {"name": "budget_period", "schema": "public", "values": ["weekly", "monthly", "quarterly", "yearly"]}, "public.goal_status": {"name": "goal_status", "schema": "public", "values": ["active", "completed", "paused", "cancelled"]}, "public.goal_type": {"name": "goal_type", "schema": "public", "values": ["savings", "debt_payoff", "budget", "investment"]}, "public.notification_type": {"name": "notification_type", "schema": "public", "values": ["bill_reminder", "budget_alert", "goal_milestone", "transaction_alert", "security_alert"]}, "public.transaction_status": {"name": "transaction_status", "schema": "public", "values": ["pending", "posted", "cancelled"]}, "public.transaction_type": {"name": "transaction_type", "schema": "public", "values": ["income", "expense", "transfer"]}, "public.payment_status": {"name": "payment_status", "schema": "public", "values": ["pending", "succeeded", "failed", "cancelled", "refunded"]}, "public.subscription_status": {"name": "subscription_status", "schema": "public", "values": ["active", "cancelled", "past_due", "unpaid", "trialing", "incomplete", "incomplete_expired"]}, "public.subscription_tier": {"name": "subscription_tier", "schema": "public", "values": ["free", "basic", "premium", "enterprise"]}, "public.usage_type": {"name": "usage_type", "schema": "public", "values": ["bank_accounts", "transactions", "ai_insights", "api_calls", "data_export"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}