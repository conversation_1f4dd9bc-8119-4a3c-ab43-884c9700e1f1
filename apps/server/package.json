{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsc && tsc-alias", "check-types": "tsc --noEmit", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "tsx watch src/index.ts", "start": "node dist/src/index.js", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:seed": "tsx src/db/seed.ts"}, "dependencies": {"@fastify/cors": "^11.0.1", "@orpc/client": "^1.5.0", "@orpc/server": "^1.5.0", "belvo": "^0.28.0", "better-auth": "^1.2.10", "dotenv": "^16.4.7", "drizzle-orm": "^0.44.2", "fastify": "^5.3.3", "node-cron": "^4.1.1", "pg": "^8.14.1", "plaid": "^36.0.0", "zod": "^3.25.16"}, "trustedDependencies": ["supabase"], "devDependencies": {"@types/belvo": "^0.16.2", "@types/node": "^22.13.11", "@types/pg": "^8.11.11", "drizzle-kit": "^0.31.2", "tsc-alias": "^1.8.11", "tsx": "^4.19.2", "typescript": "^5.8.2"}}